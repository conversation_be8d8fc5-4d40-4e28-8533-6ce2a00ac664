<template>
  <div class="airline-container">
    <!-- 地图区域 -->
    <div class="map-wrapper">
      <Amap ref="amap" :coordinates="[]" :zoom="13" />

      <!-- 鼠标经纬度显示 -->
      <div class="mouse-coordinates" v-if="showMouseCoordinates && mouseCoordinates.lng && mouseCoordinates.lat">
        <div class="coordinates-content">
          <div class="coordinate-line">
            <span class="coordinate-system">WGS84：</span>
            <span class="coordinate-value">{{ wgs84Coordinates.lng }},{{ wgs84Coordinates.lat }}</span>
          </div>
          <div class="coordinate-line">
            <span class="coordinate-system">GCJ02：</span>
            <span class="coordinate-value">{{ gcj02Coordinates.lng }},{{ gcj02Coordinates.lat }}</span>
          </div>
        </div>
      </div>

      <!-- 地图缩放倍数显示 -->
      <div class="map-zoom-display">
        <div class="zoom-content">
          <span class="zoom-label">缩放：</span>
          <span class="zoom-value">{{ mapZoomLevel.toFixed(1) }}</span>
        </div>
      </div>

      <!-- 地图工具栏 -->
      <div class="map-tools">
        <!-- 3D视角切换 -->
        <el-tooltip :content="is3DMode ? '切换到2D视角' : '切换到3D视角'" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggle3DMap"
            :class="{ 'active-tool': is3DMode }"
            class="map-tool-btn">
            <svg-icon icon-class="map_3d" />
          </el-button>
        </el-tooltip>

        <!-- 3D航线切换 -->
        <el-tooltip :content="use3DFlightPath ? '禁用3D航线' : '启用3D航线'" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggle3DFlightPath"
            :class="{ 'active-tool': use3DFlightPath }"
            class="map-tool-btn">
            <i class="el-icon-connection"></i>
          </el-button>
        </el-tooltip>

        <!-- 卫星图切换 -->
        <el-tooltip content="卫星图" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleSatelliteLayer"
            :class="{ 'active-tool': showSatelliteLayer }"
            class="map-tool-btn">
            <svg-icon icon-class="map_wxt" />
          </el-button>
        </el-tooltip>

        <!-- 路况信息 -->
        <el-tooltip content="路况信息" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggleTrafficLayer"
            :class="{ 'active-tool': showTrafficLayer }"
            class="map-tool-btn">
            <svg-icon icon-class="map_lk" />
          </el-button>
        </el-tooltip>

        <!-- 3D航点切换 -->
        <el-tooltip :content="use3DWaypoints ? '切换到2D航点' : '切换到3D航点'" placement="left">
          <el-button
            type="primary"
            size="mini"
            @click="toggle3DWaypoints"
            :class="{ 'active-tool': use3DWaypoints }"
            class="map-tool-btn">
            <i class="el-icon-view" style="font-size: 14px;"></i>
          </el-button>
        </el-tooltip>

        <!-- 测试3D航点 -->
        <el-tooltip content="测试3D航点" placement="left">
          <el-button
            type="warning"
            size="mini"
            @click="test3DWaypoint"
            class="map-tool-btn">
            <i class="el-icon-star-on" style="font-size: 14px;"></i>
          </el-button>
        </el-tooltip>
      </div>



      <!-- 悬浮控制按钮 -->
      <div class="floating-controls">
        <!-- 云台偏航 -->
        <el-button
          class="floating-btn"
          @click="handleGimbalYaw"
          title="云台偏航">
          <svg-icon icon-class="wrj_dz_wrjytph" />
        </el-button>

        <!-- 拍照 -->
        <el-button
          class="floating-btn"
          @click="handleTakePhoto"
          title="拍照">
          <svg-icon icon-class="wrj_dz_zxj" />
        </el-button>

        <!-- 变焦 -->
        <el-button
          class="floating-btn"
          @click="handleZoom"
          title="变焦">
          <svg-icon icon-class="wrj_dz_bj" />
        </el-button>

        <!-- 开始录像 -->
        <el-button
          class="floating-btn"
          :class="{ 'recording': isRecording }"
          @click="handleStartRecording"
          title="开始录像">
          <svg-icon icon-class="wrj_dz_kslx" />
        </el-button>

        <!-- 停止录像 -->
        <el-button
          class="floating-btn"
          @click="handleStopRecording"
          title="停止录像">
          <svg-icon icon-class="wrj_dz_jslx" />
        </el-button>

        <!-- 全景照片 -->
        <el-button
          class="floating-btn"
          @click="handlePanorama"
          title="全景照片">
          <svg-icon icon-class="wrj_dz_qjpz" />
        </el-button>

        <!-- 无人机偏航 -->
        <el-button
          class="floating-btn"
          @click="handleDroneYaw"
          title="无人机偏航">
          <svg-icon icon-class="wrj_dz_wrjph" />
        </el-button>

        <!-- 悬停 -->
        <el-button
          class="floating-btn"
          @click="handleHover"
          title="悬停">
          <svg-icon icon-class="wrj_dz_wrjxt" />
        </el-button>
      </div>
    </div>

    <!-- 右侧航线设置面板 -->
    <div class="airline-sidebar">
      <div class="sidebar-header">
        <div class="header-content">
          <!-- 按钮组 - 绝对定位到右侧 -->
          <div class="header-buttons">
            
            <el-button
              type="primary"
              size="mini"
              @click="saveCurrentAirline"
              class="save-button">
              保存
            </el-button>
          </div>

          <!-- 标题区域 - 完全居中 -->
          <div class="header-title-section">
            <h3>航线设置</h3>
            <div v-if="currentWaylineMode" class="wayline-mode-display">
              <span class="mode-label">当前模式：</span>
              <span class="mode-value">{{ waylineModeMap[currentWaylineMode] }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 全局设置 -->
      <div class="setting-section">
        <div class="section-title expandable" @click="toggleGlobalSettings">
          <span>全局设置</span>
          <i class="el-icon-arrow-up" :class="{ 'collapsed': !showGlobalSettings }"></i>
        </div>

        <div v-show="showGlobalSettings" class="global-settings">
          <!-- 航线名称 -->
          <div class="setting-item">
            <div class="setting-item-inline">
              <div class="setting-label">航线名称</div>
              <el-input
                v-model="currentAirline.fileName"
                placeholder="请输入航线名称（最多15个字符）"
                maxlength="15"
                show-word-limit
                size="small"
                class="filename-input-inline">
              </el-input>
            </div>
          </div>

          <!-- 全局飞行速度 -->
          <div class="setting-item">
            <div class="setting-label">全局飞行速度</div>
            <div class="speed-control">
              <el-button
                class="speed-btn decrease"
                @click="adjustSpeed(-1)"
                :disabled="currentAirline.defaultSpeed <= 1">
                −
              </el-button>
              <div class="speed-display">
                <span class="speed-value">{{ currentAirline.defaultSpeed }}</span>
                <span class="speed-unit">m/s</span>
              </div>
              <el-button
                class="speed-btn increase"
                @click="adjustSpeed(1)"
                :disabled="currentAirline.defaultSpeed >= 15">
                +
              </el-button>
            </div>
          </div>

          <!-- 全局飞行高度 -->
          <div class="setting-item">
            <div class="setting-label">全局飞行高度</div>
            <div class="speed-control">
              <el-button
                class="speed-btn decrease"
                @click="adjustHeight(-10)"
                :disabled="currentAirline.defaultHeight <= 10">
                −
              </el-button>
              <div class="speed-display">
                <span class="speed-value">{{ currentAirline.defaultHeight }}</span>
                <span class="speed-unit">m</span>
              </div>
              <el-button
                class="speed-btn increase"
                @click="adjustHeight(10)"
                :disabled="currentAirline.defaultHeight >= 500">
                +
              </el-button>
            </div>
          </div>

          <!-- 设置起飞点 -->
          <div class="setting-item">
            <div class="setting-label">起飞点设置</div>
            <div class="takeoff-point-btn-container">
              <el-button
                :type="isSettingTakeoffPoint ? 'primary' : 'default'"
                size="small"
                @click="toggleTakeoffPointSetting"
                class="takeoff-point-btn">
                <i class="el-icon-location-outline"></i>
                {{ isSettingTakeoffPoint ? '点击地图设置起飞点' : '设置起飞点' }}
              </el-button>
              <!-- 清除按钮 - 悬浮在设置按钮上方右侧 -->
              <el-button
                v-if="takeoffPoint"
                type="text"
                size="mini"
                @click="clearTakeoffPoint"
                class="clear-takeoff-btn-float">
                <i class="el-icon-delete"></i>
                清除
              </el-button>
            </div>
            <div v-if="takeoffPoint" class="takeoff-point-info">
              <div class="coord-line-single">
                <span class="coord-label">坐标：</span>
                <span class="coord-value">{{ takeoffPoint.longitude.toFixed(6) }}, {{ takeoffPoint.latitude.toFixed(6) }}</span>
              </div>
            </div>
          </div>

          <!-- 航点类型 -->
          <div class="setting-item">
            <div class="setting-label">航点类型</div>
            <el-select v-model="currentAirline.waypointType" class="full-width">
              <el-option label="直线飞行，飞行器到点悬停" value="straight_hover"></el-option>
              <el-option label="曲线飞行，飞行器到点悬停" value="curve_hover"></el-option>
              <el-option label="直线飞行，飞行器不悬停" value="straight_pass"></el-option>
            </el-select>
          </div>

          <!-- 飞行器朝向航点模式 -->
          <!-- <div class="setting-item">
            <div class="setting-label">
              飞行器朝向航点模式
              <i class="el-icon-question" title="设置飞行器在航点间飞行时的朝向"></i>
            </div>
            <el-select v-model="currentAirline.headingMode" class="full-width">
              <el-option label="沿航线方向" value="wayline"></el-option>
              <el-option label="自由朝向" value="free"></el-option>
              <el-option label="固定朝向" value="fixed"></el-option>
            </el-select>
          </div> -->

          <!-- 航点间云台俯仰控制模式 -->
          <!-- <div class="setting-item">
            <div class="setting-label">
              航点间云台俯仰控制模式
              <i class="el-icon-question" title="设置云台在航点间飞行时的俯仰控制方式"></i>
            </div>
            <el-select v-model="currentAirline.gimbalPitchMode" class="full-width">
              <el-option label="手动控制" value="manual"></el-option>
              <el-option label="自动控制" value="auto"></el-option>
              <el-option label="平滑过渡" value="smooth"></el-option>
            </el-select>
          </div> -->

        </div>
      </div>

      <!-- 航点列表 -->
      <div class="setting-section">
        <div class="section-title expandable" @click="toggleWaypointsList">
          <span>航点列表 ({{ currentAirline.waypoints.length }})</span>
          <div class="title-actions">
            <el-button
              type="text"
              size="small"
              @click.stop="batchEditWaypoints"
              title="批量编辑航点">
              <i class="el-icon-edit"></i>
            </el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="optimizeWaypoints"
              title="优化航点顺序">
              <i class="el-icon-sort"></i>
            </el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="clearAllWaypoints"
              title="清空所有航点">
              <i class="el-icon-delete"></i>
            </el-button>
            <i class="el-icon-arrow-up" :class="{ 'collapsed': !showWaypointsList }"></i>
          </div>
        </div>

        <div v-show="showWaypointsList" class="waypoints-list">
          <draggable
            v-model="currentAirline.waypoints"
            @change="onWaypointsReorder"
            handle=".waypoint-drag-handle">
            <div
              v-for="(waypoint, index) in currentAirline.waypoints"
              :key="waypoint.id"
              class="waypoint-item"
              :class="{ 'selected': selectedWaypointId === waypoint.id }"
              @click="selectWaypoint(waypoint.id)">

              <div class="waypoint-header">
                <div class="waypoint-number">{{ index + 1 }}</div>
                <div class="waypoint-drag-handle">
                  <i class="el-icon-rank"></i>
                </div>
                <div class="waypoint-title">
                  经纬度：{{ waypoint.longitude.toFixed(6) }},{{ waypoint.latitude.toFixed(6) }}
                </div>
                <el-button
                  type="text"
                  size="mini"
                  @click.stop="deleteWaypoint(waypoint.id)"
                  title="删除航点">
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>

              <div class="waypoint-details">
                <!-- 速度和高度设置 -->
                <div class="waypoint-settings">
                  <div class="setting-row">
                    <div class="setting-item-inline">
                      <span class="setting-label">速度:</span>
                      <el-input
                        v-model="waypoint.speed"
                        size="mini"
                        placeholder="默认"
                        class="setting-input"
                        @change="updateWaypoint(waypoint)">
                      </el-input>
                      <span class="setting-unit">m/s</span>
                    </div>
                    <div class="setting-item-inline">
                      <span class="setting-label">高度:</span>
                      <el-input
                        v-model="waypoint.height"
                        size="mini"
                        placeholder="默认"
                        class="setting-input"
                        @change="updateWaypoint(waypoint)">
                      </el-input>
                      <span class="setting-unit">m</span>
                    </div>
                  </div>
                </div>

                <!-- 动作图标 -->
                <div class="waypoint-actions">
                  <div class="action-icons">
                    <draggable
                      v-model="waypoint.actions"
                      @change="onActionsReorder(waypoint)"
                      handle=".action-drag-handle"
                      :group="{ name: 'actions', pull: false, put: false }"
                      class="actions-draggable">
                      <div
                        v-for="(action, actionIndex) in waypoint.actions"
                        :key="action.id"
                        class="action-item"
                        :title="getActionTooltip(action)"
                        @click.stop="editActionParams(waypoint.id, action.id)"
                        @contextmenu.prevent="showActionContextMenu($event, waypoint, action)">

                        <!-- 拖拽手柄 -->
                        <div class="action-drag-handle" title="拖拽排序">
                          <i class="el-icon-rank"></i>
                        </div>

                        <!-- 动作图标 -->
                        <div class="action-icon-wrapper">
                          <svg-icon
                            v-if="getActionIcon(action.type) !== 'default'"
                            :icon-class="getActionIcon(action.type)"
                            class="action-icon" />
                          <span v-else class="action-text">{{ getActionShortName(action.type) }}</span>
                        </div>
                      </div>
                    </draggable>
                  </div>
                </div>
              </div>
            </div>
          </draggable>
        </div>
      </div>
    </div>

    <!-- 动作参数设置对话框 -->
    <el-dialog
      :title="actionParamsDialogTitle"
      :visible.sync="actionParamsDialogVisible"
      width="500px"
      @close="onActionParamsDialogClose">
      <div v-if="currentEditingAction">
        <div class="action-info">
          <el-tag :type="getActionTagType(currentEditingAction.type)" size="medium">
            {{ getActionName(currentEditingAction.type) }}
          </el-tag>
          <span class="action-waypoint-info">
            航点 {{ currentEditingWaypointIndex + 1 }}
          </span>
        </div>

        <el-divider></el-divider>

        <!-- 云台动作参数 -->
        <div v-if="currentEditingAction.type === 'gimbal_yaw'" class="param-form">
          <el-form :model="currentEditingAction.params" label-width="100px" size="small">
            <el-form-item label="俯仰角">
              <el-input-number
                v-model="currentEditingAction.params.pitch"
                :min="-90"
                :max="30"
                :step="1">
              </el-input-number>
              <span style="margin-left: 8px;">度</span>
            </el-form-item>

            <el-form-item label="偏航角">
              <el-input-number
                v-model="currentEditingAction.params.yaw"
                :min="-180"
                :max="180"
                :step="1">
              </el-input-number>
              <span style="margin-left: 8px;">度</span>
            </el-form-item>

            <el-form-item label="转动时间">
              <el-input-number
                v-model="currentEditingAction.params.rotateTime"
                :min="1"
                :max="30"
                :step="0.1">
              </el-input-number>
              <span style="margin-left: 8px;">秒</span>
            </el-form-item>
          </el-form>
        </div>

        <!-- 变焦动作参数 -->
        <div v-if="currentEditingAction.type === 'zoom'" class="param-form">
          <el-form :model="currentEditingAction.params" label-width="100px" size="small">
            <el-form-item label="变焦倍数">
              <el-input-number
                v-model="currentEditingAction.params.factor"
                :min="1"
                :max="200"
                :step="0.1">
              </el-input-number>
              <span style="margin-left: 8px;">倍</span>
            </el-form-item>

            <el-form-item label="变焦时间">
              <el-input-number
                v-model="currentEditingAction.params.zoomTime"
                :min="0.1"
                :max="10"
                :step="0.1">
              </el-input-number>
              <span style="margin-left: 8px;">秒</span>
            </el-form-item>
          </el-form>
        </div>

        <!-- 悬停动作参数 -->
        <div v-if="currentEditingAction.type === 'hover'" class="param-form">
          <el-form :model="currentEditingAction.params" label-width="100px" size="small">
            <el-form-item label="悬停时间">
              <el-input-number
                v-model="currentEditingAction.params.duration"
                :min="1"
                :max="300"
                :step="1">
              </el-input-number>
              <span style="margin-left: 8px;">秒</span>
            </el-form-item>
          </el-form>
        </div>

        <!-- 无人机偏航动作参数 -->
        <div v-if="currentEditingAction.type === 'drone_yaw'" class="param-form">
          <el-form :model="currentEditingAction.params" label-width="100px" size="small">
            <el-form-item label="偏航角">
              <el-input-number
                v-model="currentEditingAction.params.angle"
                :min="-180"
                :max="180"
                :step="1">
              </el-input-number>
              <span style="margin-left: 8px;">度</span>
            </el-form-item>

            <el-form-item label="转动模式">
              <el-select v-model="currentEditingAction.params.mode" placeholder="请选择转动模式">
                <el-option label="顺时针" value="clockwise"></el-option>
                <el-option label="逆时针" value="counterclockwise"></el-option>
                <el-option label="最短路径" value="shortest"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>

      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelActionParams">取消</el-button>
        <el-button @click="resetActionParams">重置</el-button>
        <el-button type="primary" @click="saveActionParams">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量编辑航点对话框 -->
    <el-dialog
      title="批量编辑航点"
      :visible.sync="batchEditDialogVisible"
      width="600px"
      @close="onBatchEditDialogClose">

      <div class="batch-edit-content">
        <el-form :model="batchEditForm" label-width="100px" size="small">
          <el-form-item label="批量设置">
            <el-checkbox-group v-model="batchEditOptions">
              <el-checkbox label="height">高度</el-checkbox>
              <el-checkbox label="speed">速度</el-checkbox>
              <el-checkbox label="actions">动作</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="高度" v-if="batchEditOptions.includes('height')">
            <el-input-number
              v-model="batchEditForm.height"
              :min="10"
              :max="500"
              :step="1">
            </el-input-number>
            <span style="margin-left: 8px;">米</span>
          </el-form-item>

          <el-form-item label="速度" v-if="batchEditOptions.includes('speed')">
            <el-input-number
              v-model="batchEditForm.speed"
              :min="1"
              :max="15"
              :step="0.1">
            </el-input-number>
            <span style="margin-left: 8px;">m/s</span>
          </el-form-item>

          <el-form-item label="应用范围">
            <el-radio-group v-model="batchEditForm.applyTo">
              <el-radio label="all">所有航点</el-radio>
              <el-radio label="selected">选中航点</el-radio>
              <el-radio label="range">指定范围</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="航点范围" v-if="batchEditForm.applyTo === 'range'">
            <el-input-number
              v-model="batchEditForm.startIndex"
              :min="1"
              :max="currentAirline.waypoints.length"
              placeholder="起始航点">
            </el-input-number>
            <span style="margin: 0 8px;">到</span>
            <el-input-number
              v-model="batchEditForm.endIndex"
              :min="1"
              :max="currentAirline.waypoints.length"
              placeholder="结束航点">
            </el-input-number>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="applyBatchEdit">应用</el-button>
      </div>
    </el-dialog>

   

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuPosition.x + 'px', top: contextMenuPosition.y + 'px' }"
      @click.stop>
      <div class="context-menu-item" @click="deleteActionFromContextMenu">
        <i class="el-icon-delete"></i>
        删除动作
      </div>
    </div>

    <!-- 点击遮罩层关闭右键菜单 -->
    <div
      v-show="contextMenuVisible"
      class="context-menu-overlay"
      @click="hideContextMenu">
    </div>
  </div>
</template>

<script>
/* global AMap */
import Amap from '@/components/Amap'
import mapMixin from '@/views/flightmaster/mixins/mapMixin.js'
import airlineMixin from '@/views/flightmaster/mixins/airlineMixin.js'
import draggable from 'vuedraggable'
import { createWayline, getWaylineDetail } from '@/api/flightAirline/airline'
import { ThreeWaypointManager } from '@/utils/ThreeWaypointManager'
import { ThreeFlightPathManager } from '@/utils/ThreeFlightPathManager'

export default {
  name: 'FlightAirline',
  components: {
    Amap,
    draggable
  },
  mixins: [mapMixin, airlineMixin],
  data() {
    return {
      // 记录进入页面前的sidebar状态
      originalSidebarState: true,

      // 数据变更标记
      hasUnsavedChanges: false,

      // 当前工具
      currentTool: 'select',

      // 键盘状态
      isShiftPressed: false,
      isCtrlPressed: false,

      // 3D航点管理器
      threeWaypointManager: null,
      use3DWaypoints: false, // 是否使用3D航点，默认关闭

      // 3D航线管理器
      threeFlightPathManager: null,
      use3DFlightPath: false, // 是否使用3D航线，默认关闭

      // 拖拽状态
      isDragging: false,
      isHeightDragging: false,
      dragStartPosition: null,
      dragStartHeight: 0,

      // 防止航点点击触发地图点击的标志
      isWaypointClicking: false,

      // 鼠标坐标
      mouseCoordinates: {
        lng: null,
        lat: null
      },
      showMouseCoordinates: true,

      // 默认航线（航点飞行）
      currentAirline: {
        id: 'default_waypoint_flight',
        name: '航点飞行',
        description: '默认航点飞行航线',
        fileName: '', // 航线名称
        type: 'waypoint',
        defaultHeight: 127,
        defaultSpeed: 10,
        takeoffSpeed: 15,
        waypointType: 'straight_hover',
        headingMode: 'wayline',
        gimbalPitchMode: 'manual',
        finishAction: 'return_home',
        waypoints: [],
        createTime: new Date(),
        updateTime: new Date()
      },

      // 选中的航点
      selectedWaypointId: null,



      // 撤销重做
      actionHistory: [],
      actionHistoryIndex: -1,

      // 地图标记
      waypointMarkers: {},
      waypointLines: [],
      distanceLabels: [], // 距离标签

      // UI状态
      showGlobalSettings: true,
      showWaypointsList: true,

      // 起飞点相关
      takeoffPoint: null,
      isSettingTakeoffPoint: false,
      takeoffPointMarker: null,
      mouseFollowMarker: null,

      // 控制状态
      isRecording: false,

      // 地图状态
      mapZoomLevel: 10,
      mouseCoordinates: { lng: 0, lat: 0 },
      is3DMode: false,
      showSatelliteLayer: false,
      showTrafficLayer: false,
      satelliteLayer: null,
      trafficLayer: null,

      // 右键菜单状态
      contextMenuVisible: false,
      contextMenuPosition: { x: 0, y: 0 },
      contextMenuWaypoint: null,
      contextMenuAction: null,

      // 对话框
      airlineDialogVisible: false,
      airlineDialogTitle: '新建航线',
      airlineForm: {
        name: '',
        description: '',
        type: 'normal',
        defaultHeight: 50,
        defaultSpeed: 5
      },
      airlineRules: {
        name: [
          { required: true, message: '请输入航线名称', trigger: 'blur' }
        ]
      },

      // 动作参数编辑
      actionParamsDialogVisible: false,
      currentEditingAction: null,
      currentEditingWaypointIndex: -1,

      // 动作类型映射
      actionTypes: {
        takePhoto: { name: '单拍', tag: 'success' },
        startRecord: { name: '开始录像', tag: 'primary' },
        stopRecord: { name: '结束录像', tag: 'danger' },
        focus: { name: '对焦', tag: 'info' },
        zoom: { name: '变焦', tag: 'info' },
        customDirName: { name: '创建新文件夹', tag: 'warning' },
        gimbalRotate: { name: '旋转云台', tag: 'info' },
        rotateYaw: { name: '飞行器偏航', tag: 'warning' },
        hover: { name: '悬停等待', tag: 'warning' },
        gimbalEvenlyRotate: { name: '航段间均匀转动云台pitch角', tag: 'info' },
        orientedShoot: { name: '定向拍照动作', tag: 'success' },
        panoShot: { name: '全景拍照动作', tag: 'success' },
        recordPointCloud: { name: '点云录制操作', tag: 'primary' }
      },

      // 当前选择的航线模式
      currentWaylineMode: null,

      // 航线模式映射
      waylineModeMap: {
        waypoint: '航点飞行',
        mapping2d: '建图航拍',
        mapping3d: '倾斜摄影',
        mappingStrip: '航带飞行'
      },

      // 批量编辑相关
      batchEditDialogVisible: false,
      batchEditOptions: [],
      batchEditForm: {
        height: 100,
        speed: 5,
        applyTo: 'all',
        startIndex: 1,
        endIndex: 1
      },

      // 3D地图控制插件
      controlBar: null,
      toolBar: null,

      // 高度拖拽状态
      isHeightDragging: false
    }
  },

  computed: {
    // WGS84坐标
    wgs84Coordinates() {
      if (!this.mouseCoordinates.lng || !this.mouseCoordinates.lat) {
        return { lng: '', lat: '' }
      }
      const wgs84 = this.gcj02ToWgs84(this.mouseCoordinates.lng, this.mouseCoordinates.lat)
      return {
        lng: wgs84.lng.toFixed(6),
        lat: wgs84.lat.toFixed(6)
      }
    },

    // GCJ02坐标
    gcj02Coordinates() {
      if (!this.mouseCoordinates.lng || !this.mouseCoordinates.lat) {
        return { lng: '', lat: '' }
      }
      return {
        lng: this.mouseCoordinates.lng.toFixed(6),
        lat: this.mouseCoordinates.lat.toFixed(6)
      }
    },

    // 是否可以撤销
    canUndo() {
      return this.actionHistoryIndex >= 0
    },

    // 是否可以重做
    canRedo() {
      return this.actionHistoryIndex < this.actionHistory.length - 1
    },



    // 动作参数对话框标题
    actionParamsDialogTitle() {
      if (!this.currentEditingAction) return '动作参数设置'
      return `${this.getActionName(this.currentEditingAction.type)} - 参数设置`
    }
  },

  mounted() {
    // 处理路由参数，设置航线模式
    this.handleRouteParams()

    // 检查是否有航线ID参数，如果有则加载航线详情
    const waylineId = this.$route.params.waylineId || this.$route.query.id
    if (waylineId) {
      this.loadWaylineDetail(waylineId)
    } else {
      this.loadCurrentAirline()
    }

    this.initMap()

    // 添加全局点击事件监听器，用于隐藏右键菜单
    document.addEventListener('click', this.hideContextMenu)

    // 添加键盘事件监听器
    document.addEventListener('keydown', this.onKeyDown)
    document.addEventListener('keyup', this.onKeyUp)

    // 添加页面关闭前的提示
    window.addEventListener('beforeunload', this.beforeUnloadHandler)

    // 进入页面时收起左侧sidebar
    this.collapseSidebar()
  },

  beforeDestroy() {
    // 清理3D航点管理器
    if (this.threeWaypointManager) {
      this.threeWaypointManager.dispose()
      this.threeWaypointManager = null
    }

    // 清理3D航线管理器
    if (this.threeFlightPathManager) {
      this.threeFlightPathManager.dispose()
      this.threeFlightPathManager = null
    }

    // 移除全局点击事件监听器
    document.removeEventListener('click', this.hideContextMenu)

    // 移除键盘事件监听器
    document.removeEventListener('keydown', this.onKeyDown)
    document.removeEventListener('keyup', this.onKeyUp)

    // 移除页面关闭前的提示
    window.removeEventListener('beforeunload', this.beforeUnloadHandler)

    // 离开页面时恢复sidebar状态
    this.restoreSidebar()
  },

  // 路由守卫
  beforeRouteLeave(to, from, next) {
    if (this.hasUnsavedChanges) {
      this.$confirm('数据未保存，是否确定离开？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确定离开，关闭当前页面并返回上一级
        this.goBackAndRefresh()
        next(false) // 阻止默认路由跳转，因为我们手动处理了页面跳转
      }).catch(() => {
        next(false)
      })
    } else {
      next()
    }
  },

  methods: {
    // 页面关闭前的处理
    beforeUnloadHandler(event) {
      if (this.hasUnsavedChanges) {
        const message = '数据未保存，确定要离开吗？'
        event.returnValue = message
        return message
      }
    },

    // 标记数据已变更
    markDataChanged() {
      this.hasUnsavedChanges = true
    },

   

    // 收起左侧sidebar
    collapseSidebar() {
      // 记录进入页面前的sidebar状态
      this.originalSidebarState = this.$store.state.app.sidebar.opened

      // 如果sidebar是展开的，则收起它
      if (this.$store.state.app.sidebar.opened) {
        this.$store.dispatch('app/toggleSideBar')
      }
    },

    // 恢复sidebar状态
    restoreSidebar() {
      // 如果原来sidebar是展开的，且现在是收起的，则恢复展开状态
      if (this.originalSidebarState && !this.$store.state.app.sidebar.opened) {
        this.$store.dispatch('app/toggleSideBar')
      }
    },

    // 处理路由参数
    handleRouteParams() {
      const mode = this.$route.query.mode
      if (mode && this.waylineModeMap[mode]) {
        this.currentWaylineMode = mode
        // 根据模式更新当前航线的类型和名称
        this.currentAirline.type = mode
        this.currentAirline.name = this.waylineModeMap[mode]
        console.log('设置航线模式:', mode, this.waylineModeMap[mode])
      }
    },

    // 初始化地图
    initMap() {
      this.$nextTick(() => {
        if (this.$refs.amap && this.$refs.amap.map) {
          console.log('开始绑定地图事件')

          // 移除可能存在的旧事件监听器
          this.$refs.amap.map.off('mousemove', this.onMouseMove)

          // 关闭地图双击放大事件
          this.$refs.amap.map.setStatus({
            doubleClickZoom: false
          })
          console.log('地图双击放大已禁用')

          // 添加地图点击事件 - 使用高德地图原生事件获取准确坐标
          this.$refs.amap.map.on('click', this.onMapClick)
          console.log('地图点击事件已绑定')

          // 添加地图右键点击事件
          this.$refs.amap.map.on('rightclick', this.onMapRightClick)
          console.log('地图右键点击事件已绑定')

          // 添加鼠标移动事件
          this.$refs.amap.map.on('mousemove', this.onMouseMove)
          console.log('地图鼠标移动事件已绑定')

          // 如果有保存的航点，显示它们
          if (this.currentAirline.waypoints.length > 0) {
            this.currentAirline.waypoints.forEach(waypoint => {
              this.createWaypointMarker(waypoint)
            })
          }

          // 如果有保存的起飞点，显示它
          if (this.takeoffPoint) {
            this.createTakeoffPointMarker()
          }

          // 更新连线
          this.updateWaypointLines()

          // 初始化地图缩放级别
          this.mapZoomLevel = this.$refs.amap.map.getZoom()

          // 监听地图缩放事件
          this.$refs.amap.map.on('zoomend', () => {
            this.mapZoomLevel = this.$refs.amap.map.getZoom()
          })

          // 检测初始视角状态
          this.detectInitial3DState()

          // 延迟初始化3D航点管理器，确保地图完全加载
          if (this.use3DWaypoints) {
            setTimeout(() => {
              this.initThreeWaypointManager()
            }, 2000) // 延迟2秒确保地图完全初始化
          }

          console.log('航线规划地图初始化完成')
        } else {
          console.log('地图还未初始化，延迟重试')
          // 如果地图还没有初始化，延迟重试
          setTimeout(() => {
            this.initMap()
          }, 500)
        }
      })
    },

    // 地图点击事件
    onMapClick(e) {
      console.log('地图点击事件触发', e.lnglat, 'Shift键状态:', this.isShiftPressed)

      // 如果是航点点击触发的，忽略此次地图点击
      if (this.isWaypointClicking) {
        console.log('忽略航点点击触发的地图事件')
        this.isWaypointClicking = false
        return
      }

      if (this.isSettingTakeoffPoint) {
        this.setTakeoffPoint(e.lnglat.lng, e.lnglat.lat)
        return
      }

      // 根据Shift键状态和点击类型处理不同逻辑
      if (this.isShiftPressed) {
        console.log('检测到Shift+左键点击，选中航点ID:', this.selectedWaypointId)
        if (this.selectedWaypointId) {
          // Shift+左键：在选中点后面添加航点
          this.insertWaypointAfterSelected(e.lnglat.lng, e.lnglat.lat)
        } else {
          // 没有选中航点时，直接添加到末尾
          console.log('没有选中航点，添加到末尾')
          this.addWaypoint(e.lnglat.lng, e.lnglat.lat)
        }
      } else {
        // 普通左键：创建航点
         console.log(' 普通左键：创建航点:')
        this.addWaypoint(e.lnglat.lng, e.lnglat.lat)
      }
    },

    // 地图右键点击事件
    onMapRightClick(e) {
      console.log('地图右键点击事件触发', e.lnglat, 'Shift键状态:', this.isShiftPressed)

      if (this.isSettingTakeoffPoint) {
        // 设置起飞点模式下，右键取消设置
        this.toggleTakeoffPointSetting()
        return
      }

      // 根据Shift键状态处理不同逻辑
      if (this.isShiftPressed) {
        console.log('检测到Shift+右键点击，选中航点ID:', this.selectedWaypointId)
        if (this.selectedWaypointId) {
          // Shift+右键：在选中点前面添加航点
          this.insertWaypointBeforeSelected(e.lnglat.lng, e.lnglat.lat)
        } else {
          // 没有选中航点时，直接添加到末尾
          console.log('没有选中航点，添加到末尾')
          this.addWaypoint(e.lnglat.lng, e.lnglat.lat)
        }
      } else {
        // 普通右键：删除最近的航点
        this.deleteNearestWaypoint(e.lnglat.lng, e.lnglat.lat)
      }
    },

    // 鼠标移动事件
    onMouseMove(e) {
      this.mouseCoordinates = {
        lng: e.lnglat.lng,
        lat: e.lnglat.lat
      }

      // 如果正在设置起飞点，更新跟随鼠标的标记位置
      if (this.isSettingTakeoffPoint && this.mouseFollowMarker) {
        this.mouseFollowMarker.setPosition([e.lnglat.lng, e.lnglat.lat])
      }
    },

    // 键盘按下事件
    onKeyDown(e) {
      if (e.key === 'Shift' || e.shiftKey) {
        this.isShiftPressed = true
        console.log('Shift键按下，当前状态:', this.isShiftPressed)
      }
      if (e.key === 'Control' || e.ctrlKey) {
        this.isCtrlPressed = true
        console.log('Ctrl键按下，当前状态:', this.isCtrlPressed)
        if (this.$refs.amap && this.$refs.amap.map) {
            this.$refs.amap.map.setStatus({ rotateEnable: false, pitchEnable: false })
          }
      }
    },

    // 键盘释放事件
    onKeyUp(e) {
      if (e.key === 'Shift' || !e.shiftKey) {
        this.isShiftPressed = false
        console.log('Shift键释放，当前状态:', this.isShiftPressed)
      }
      if (e.key === 'Control' || !e.ctrlKey) {
        this.isCtrlPressed = false
        console.log('Ctrl键释放，当前状态:', this.isCtrlPressed)
        if (this.$refs.amap && this.$refs.amap.map) {
            this.$refs.amap.map.setStatus({ rotateEnable: true, pitchEnable: true })
          }
      }
    },



    // 设置工具
    setTool(tool) {
      this.currentTool = tool

      // 更改鼠标样式
      if (this.$refs.amap && this.$refs.amap.map) {
        const mapContainer = this.$refs.amap.map.getContainer()
        switch (tool) {
          case 'waypoint':
            mapContainer.style.cursor = 'crosshair'
            break
          case 'line':
            mapContainer.style.cursor = 'pointer'
            break
          default:
            mapContainer.style.cursor = 'default'
        }
      }
    },

    // 添加航点
    addWaypoint(lng, lat, height = null) {
      if (!this.currentAirline) {
        this.$message.warning('请先选择或创建一个航线')
        return
      }

      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      // 如果没有指定高度，使用默认高度
      const defaultHeight = height !== null ? height : (this.currentAirline.globalHeight || 100)

      const waypoint = {
        id: this.generateId(),
        longitude: wgs84.lng,
        latitude: wgs84.lat,
        height: defaultHeight,
        speed: null, // 默认为空，使用全局速度
        actions: [],
        createTime: new Date()
      }

      this.currentAirline.waypoints.push(waypoint)

      // 创建传统2D标记
      this.createWaypointMarker(waypoint)

      // 如果启用了3D航点且管理器已初始化，创建3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          const result = this.threeWaypointManager.addWaypoint(
            waypoint.id,
            waypoint.longitude,
            waypoint.latitude,
            waypoint.height,
            {
              index: this.currentAirline.waypoints.length,
              speed: waypoint.speed,
              actions: waypoint.actions
            }
          )
          console.log('3D航点创建成功:', result)
        } catch (error) {
          console.error('3D航点创建失败:', error)
          this.$message.warning('3D航点创建失败，已使用2D模式')
        }
      }

      this.updateWaypointLines()

      // 更新3D航线
      if (this.use3DFlightPath && this.currentAirline.waypoints.length >= 2) {
        this.create3DFlightPath()
      }

      // 记录操作历史
      this.recordAction('add_waypoint', { waypoint: { ...waypoint } })

      // 标记数据已变更
      this.markDataChanged()

      console.log('添加航点:', waypoint.id, '坐标:', wgs84, '高度:', defaultHeight)

      return waypoint
    },

    // 删除最近的航点
    deleteNearestWaypoint(lng, lat) {
      if (!this.currentAirline || this.currentAirline.waypoints.length === 0) {
        this.$message.warning('没有航点可删除')
        return
      }

      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      // 找到最近的航点
      let nearestWaypoint = null
      let nearestDistance = Infinity
      let nearestIndex = -1

      this.currentAirline.waypoints.forEach((waypoint, index) => {
        const distance = this.calculateDistance(wgs84, waypoint)
        if (distance < nearestDistance) {
          nearestDistance = distance
          nearestWaypoint = waypoint
          nearestIndex = index
        }
      })

      if (nearestWaypoint) {
        // 记录操作历史
        this.recordAction('delete_waypoint', { waypoint: { ...nearestWaypoint }, index: nearestIndex })

        // 删除航点
        this.currentAirline.waypoints.splice(nearestIndex, 1)

        // 删除标记
        if (this.waypointMarkers[nearestWaypoint.id]) {
          this.waypointMarkers[nearestWaypoint.id].setMap(null)
          delete this.waypointMarkers[nearestWaypoint.id]
        }

        // 更新连线和标记序号
        this.updateWaypointMarkers()
        this.updateWaypointLines()

        console.log('删除最近航点:', nearestWaypoint.id, '距离:', nearestDistance.toFixed(2) + 'm')
        this.$message.success(`已删除航点 (距离: ${nearestDistance.toFixed(0)}m)`)
      }
    },

    // 在选中点后面插入航点
    insertWaypointAfterSelected(lng, lat, height = null) {
      if (!this.currentAirline) {
        this.$message.warning('请先选择或创建一个航线')
        return
      }

      if (!this.selectedWaypointId) {
        this.$message.warning('请先选择一个航点')
        return
      }

      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      // 如果没有指定高度，使用默认高度
      const defaultHeight = height !== null ? height : (this.currentAirline.globalHeight || 100)

      const waypoint = {
        id: this.generateId(),
        longitude: wgs84.lng,
        latitude: wgs84.lat,
        height: defaultHeight,
        speed: null,
        actions: [],
        createTime: new Date()
      }

      // 找到选中航点的索引
      const selectedIndex = this.currentAirline.waypoints.findIndex(w => w.id === this.selectedWaypointId)
      if (selectedIndex !== -1) {
        // 在选中航点后面插入新航点
        this.currentAirline.waypoints.splice(selectedIndex + 1, 0, waypoint)

        this.createWaypointMarker(waypoint)

        // 如果启用了3D航点且管理器已初始化，创建3D航点
        if (this.use3DWaypoints && this.threeWaypointManager) {
          try {
            const result = this.threeWaypointManager.addWaypoint(
              waypoint.id,
              waypoint.longitude,
              waypoint.latitude,
              waypoint.height,
              {
                index: selectedIndex + 2,
                speed: waypoint.speed,
                actions: waypoint.actions
              }
            )
            console.log('3D航点插入成功:', result)
          } catch (error) {
            console.error('3D航点插入失败:', error)
          }
        }

        this.updateWaypointMarkers()
        this.updateWaypointLines()

        // 记录操作历史
        this.recordAction('add_waypoint', { waypoint: { ...waypoint } })

        console.log('在航点后插入:', waypoint.id, '位置:', selectedIndex + 1, '高度:', defaultHeight)
        this.$message.success(`已在航点 ${selectedIndex + 1} 后面添加新航点`)
      }
    },

    // 在选中点前面插入航点
    insertWaypointBeforeSelected(lng, lat, height = null) {
      if (!this.currentAirline) {
        this.$message.warning('请先选择或创建一个航线')
        return
      }

      if (!this.selectedWaypointId) {
        this.$message.warning('请先选择一个航点')
        return
      }

      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      // 如果没有指定高度，使用默认高度
      const defaultHeight = height !== null ? height : (this.currentAirline.globalHeight || 100)

      const waypoint = {
        id: this.generateId(),
        longitude: wgs84.lng,
        latitude: wgs84.lat,
        height: defaultHeight,
        speed: null,
        actions: [],
        createTime: new Date()
      }

      // 找到选中航点的索引
      const selectedIndex = this.currentAirline.waypoints.findIndex(w => w.id === this.selectedWaypointId)
      if (selectedIndex !== -1) {
        // 在选中航点前面插入新航点
        this.currentAirline.waypoints.splice(selectedIndex, 0, waypoint)

        this.createWaypointMarker(waypoint)

        // 如果启用了3D航点且管理器已初始化，创建3D航点
        if (this.use3DWaypoints && this.threeWaypointManager) {
          try {
            const result = this.threeWaypointManager.addWaypoint(
              waypoint.id,
              waypoint.longitude,
              waypoint.latitude,
              waypoint.height,
              {
                index: selectedIndex + 1,
                speed: waypoint.speed,
                actions: waypoint.actions
              }
            )
            console.log('3D航点前插入成功:', result)
          } catch (error) {
            console.error('3D航点前插入失败:', error)
          }
        }

        this.updateWaypointMarkers()
        this.updateWaypointLines()

        // 记录操作历史
        this.recordAction('add_waypoint', { waypoint: { ...waypoint } })

        console.log('在航点前插入:', waypoint.id, '位置:', selectedIndex, '高度:', defaultHeight)
        this.$message.success(`已在航点 ${selectedIndex + 1} 前面添加新航点`)
      }
    },

    // 根据ID删除特定航点
    deleteWaypointById(waypointId) {
      if (!this.currentAirline || this.currentAirline.waypoints.length === 0) {
        this.$message.warning('没有航点可删除')
        return
      }

      // 找到要删除的航点
      const waypointIndex = this.currentAirline.waypoints.findIndex(w => w.id === waypointId)
      if (waypointIndex === -1) {
        this.$message.warning('未找到要删除的航点')
        return
      }

      const waypoint = this.currentAirline.waypoints[waypointIndex]

      // 记录操作历史
      this.recordAction('delete_waypoint', { waypoint: { ...waypoint }, index: waypointIndex })

      // 删除航点
      this.currentAirline.waypoints.splice(waypointIndex, 1)

      // 删除标记
      if (this.waypointMarkers[waypointId]) {
        this.waypointMarkers[waypointId].setMap(null)
        delete this.waypointMarkers[waypointId]
      }

      // 如果删除的是选中的航点，清除选中状态
      if (this.selectedWaypointId === waypointId) {
        this.selectedWaypointId = null
      }

      // 更新连线和标记序号
      this.updateWaypointMarkers()
      this.updateWaypointLines()

      // 标记数据已变更
      this.markDataChanged()

      console.log('删除航点:', waypointId, '原位置:', waypointIndex + 1)
      this.$message.success(`已删除航点 ${waypointIndex + 1}`)
    },

    // 创建航点标记
    createWaypointMarker(waypoint) {
      if (!this.$refs.amap || !this.$refs.amap.map) return

      const index = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
      const gcj02 = this.wgs84ToGcj02(waypoint.longitude, waypoint.latitude)

      console.log('创建航点标记:', waypoint.id, '序号:', index + 1, '坐标:', gcj02)

      // 创建3D航点标记
      const markerContent = this.create3DWaypointMarker(waypoint, index + 1)

      const marker = new window.AMap.Marker({
        position: [gcj02.lng, gcj02.lat],
        content: markerContent,
        offset: new window.AMap.Pixel(-18, -18), // 调整偏移以适应新的航点样式
        extData: { waypointId: waypoint.id },
        draggable: true, // 启用拖拽
        clickable: true, // 确保标记可点击
        bubble: false, // 禁用事件冒泡
        // 确保标记在地图旋转时位置固定
        anchor: 'center',
        autoRotation: false // 禁用自动旋转
      })

      // 添加点击事件
      marker.on('click', () => {
        console.log('航点标记点击事件:', waypoint.id)
        this.isWaypointClicking = true // 设置标志防止触发地图点击
        this.selectWaypoint(waypoint.id)
      })

      // 添加右键点击事件 - 直接删除该航点
      marker.on('rightclick', (e) => {
        console.log('航点标记右键点击事件:', waypoint.id)
        this.isWaypointClicking = true // 设置标志防止触发地图右键点击
        this.deleteWaypointById(waypoint.id)

        // 阻止事件冒泡到地图
        if (e && e.stopPropagation) {
          e.stopPropagation()
        }
        this.isWaypointClicking = false
        return false
      })

      // 添加增强的拖拽事件（支持Ctrl+拖拽改变高度）
      this.addEnhancedDragEvents(marker, waypoint)

      marker.setMap(this.$refs.amap.map)
      this.waypointMarkers[waypoint.id] = marker

      console.log('航点标记创建完成:', waypoint.id, marker)
    },

    // 更新航点连线
    updateWaypointLines() {
      if (!this.currentAirline || !this.$refs.amap || !this.$refs.amap.map) return

      // 清除现有连线
      this.waypointLines.forEach(line => {
        line.setMap(null)
      })
      this.waypointLines = []

      // 清除现有距离标签
      this.distanceLabels.forEach(label => {
        label.setMap(null)
      })
      this.distanceLabels = []

      const waypoints = this.currentAirline.waypoints
      if (waypoints.length === 0) return

      // 如果有起飞点，从起飞点连接到第一个航点
      if (this.takeoffPoint && waypoints.length > 0) {
        const takeoffGcj02 = this.wgs84ToGcj02(this.takeoffPoint.longitude, this.takeoffPoint.latitude)
        const firstWaypointGcj02 = this.wgs84ToGcj02(waypoints[0].longitude, waypoints[0].latitude)

        const takeoffLine = new AMap.Polyline({
          path: [
            [takeoffGcj02.lng, takeoffGcj02.lat],
            [firstWaypointGcj02.lng, firstWaypointGcj02.lat]
          ],
          strokeColor: '#52c41a', // 绿色表示起飞线
          strokeWeight: 3,
          strokeStyle: 'solid',
          strokeOpacity: 0.8,
          showDir: true
        })

        takeoffLine.setMap(this.$refs.amap.map)
        this.waypointLines.push(takeoffLine)

        // 添加起飞点到第一个航点的距离标签
        const takeoffDistance = this.calculateDistance(this.takeoffPoint, waypoints[0])
        this.createDistanceLabel(takeoffGcj02, firstWaypointGcj02, takeoffDistance)
      }

      // 创建航点间连线
      for (let i = 0; i < waypoints.length - 1; i++) {
        const start = waypoints[i]
        const end = waypoints[i + 1]

        const startGcj02 = this.wgs84ToGcj02(start.longitude, start.latitude)
        const endGcj02 = this.wgs84ToGcj02(end.longitude, end.latitude)

        const polyline = new AMap.Polyline({
          path: [
            [startGcj02.lng, startGcj02.lat],
            [endGcj02.lng, endGcj02.lat]
          ],
          strokeColor: '#1890ff',
          strokeWeight: 3,
          strokeStyle: 'solid',
          strokeOpacity: 0.8,
          showDir: true // 显示方向箭头
        })

        polyline.setMap(this.$refs.amap.map)
        this.waypointLines.push(polyline)

        // 添加航点间距离标签
        const distance = this.calculateDistance(start, end)
        this.createDistanceLabel(startGcj02, endGcj02, distance)
      }
    },

    // 创建距离标签
    createDistanceLabel(startCoord, endCoord, distance) {
      if (!this.$refs.amap || !this.$refs.amap.map) return

      // 计算中点位置
      const midLng = (startCoord.lng + endCoord.lng) / 2
      const midLat = (startCoord.lat + endCoord.lat) / 2

      // 格式化距离显示
      const distanceText = this.formatDistance(distance)

      // 创建距离标签内容
      const labelContent = document.createElement('div')
      labelContent.className = 'distance-label'
      labelContent.innerHTML = `
        <div class="distance-text">${distanceText}</div>
      `

      // 添加内联样式
      labelContent.style.cssText = `
        position: relative;
        pointer-events: none;
      `

      const textDiv = labelContent.querySelector('.distance-text')
      if (textDiv) {
        textDiv.style.cssText = `
          color: #1890ff;
          font-size: 12px;
          font-weight: bold;
          white-space: nowrap;
          text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        `
      }

      const distanceMarker = new AMap.Marker({
        position: [midLng, midLat],
        content: labelContent,
        offset: new AMap.Pixel(-20, -10),
        zIndex: 100
      })

      distanceMarker.setMap(this.$refs.amap.map)
      this.distanceLabels.push(distanceMarker)
    },

    // 格式化距离显示
    formatDistance(distance) {
      if (distance < 1000) {
        return `${Math.round(distance)}m`
      } else {
        return `${(distance / 1000).toFixed(2)}km`
      }
    },

    // 选择航点
    selectWaypoint(waypointId) {
      this.selectedWaypointId = waypointId

      // 如果使用3D航点管理器，同步选中状态
      if (this.use3DWaypoints && this.threeWaypointManager) {
        this.threeWaypointManager.selectWaypoint(waypointId)
      } else {
        // 高亮选中的传统2D航点
        Object.keys(this.waypointMarkers).forEach(id => {
          const marker = this.waypointMarkers[id]
          const content = marker.getContent()
          if (id === waypointId) {
            content.classList.add('selected')
          } else {
            content.classList.remove('selected')
          }
        })
      }

      console.log('选中航点:', waypointId)
    },

    // 保存动作参数
    saveActionParams() {
      if (!this.currentEditingAction) return

      const waypoint = this.currentAirline.waypoints[this.currentEditingWaypointIndex]
      if (!waypoint) return

      // 查找是否是编辑现有动作
      const existingActionIndex = waypoint.actions.findIndex(a => a.id === this.currentEditingAction.id)

      if (existingActionIndex !== -1) {
        // 更新现有动作
        waypoint.actions[existingActionIndex] = { ...this.currentEditingAction }
      } else {
        // 添加新动作，确保使用正确的序号ID
        const newAction = { ...this.currentEditingAction }
        newAction.id = waypoint.actions.length + 1
        waypoint.actions.push(newAction)
      }

      this.actionParamsDialogVisible = false
      this.$message.success('动作参数已保存')
    },

    // 取消动作参数设置
    cancelActionParams() {
      this.actionParamsDialogVisible = false
    },

    // 重置动作参数
    resetActionParams() {
      if (!this.currentEditingAction) return

      this.currentEditingAction.params = this.getDefaultActionParams(this.currentEditingAction.type)
      this.$message.success('参数已重置为默认值')
    },

    // 对话框关闭事件
    onActionParamsDialogClose() {
      this.currentEditingAction = null
      this.currentEditingWaypointIndex = -1
    },

    // 更新航点位置
    updateWaypointPosition(waypointId, lng, lat) {
      const waypoint = this.currentAirline.waypoints.find(w => w.id === waypointId)
      if (!waypoint) return

      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      // 记录操作历史
      const oldPosition = {
        longitude: waypoint.longitude,
        latitude: waypoint.latitude
      }

      // 更新航点坐标
      waypoint.longitude = wgs84.lng
      waypoint.latitude = wgs84.lat

      // 如果启用了3D航点且管理器已初始化，同步更新3D航点位置
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          // 删除旧的3D航点
          this.threeWaypointManager.removeWaypoint(waypointId)
          // 创建新的3D航点
          this.threeWaypointManager.addWaypoint(
            waypointId,
            waypoint.longitude,
            waypoint.latitude,
            waypoint.height,
            {
              speed: waypoint.speed,
              actions: waypoint.actions
            }
          )
          console.log('3D航点位置更新成功:', waypointId, wgs84)
        } catch (error) {
          console.error('3D航点位置更新失败:', error)
        }
      }

      // 更新连线
      this.updateWaypointLines()

      // 强制更新Vue组件以同步右侧列表显示
      this.$forceUpdate()

      // 标记数据已变更
      this.markDataChanged()

      // 记录操作历史
      this.recordAction('move_waypoint', {
        waypointId,
        oldPosition,
        newPosition: { longitude: wgs84.lng, latitude: wgs84.lat }
      })

      console.log(`航点 ${waypointId} 位置已更新:`, wgs84)
    },

    // 删除航点
    deleteWaypoint(waypointId) {
      if (!this.currentAirline) return

      const index = this.currentAirline.waypoints.findIndex(w => w.id === waypointId)
      if (index === -1) return

      const waypoint = this.currentAirline.waypoints[index]

      // 记录操作历史
      this.recordAction('delete_waypoint', { waypoint: { ...waypoint }, index })

      // 删除航点
      this.currentAirline.waypoints.splice(index, 1)

      // 删除2D标记
      if (this.waypointMarkers[waypointId]) {
        this.waypointMarkers[waypointId].setMap(null)
        delete this.waypointMarkers[waypointId]
      }

      // 删除3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          this.threeWaypointManager.removeWaypoint(waypointId)
          console.log('3D航点删除成功:', waypointId)
        } catch (error) {
          console.error('3D航点删除失败:', error)
        }
      }

      // 如果删除的是选中的航点，清除选中状态
      if (this.selectedWaypointId === waypointId) {
        this.selectedWaypointId = null
      }

      // 更新连线和标记序号
      this.updateWaypointMarkers()
      this.updateWaypointLines()

      // 更新3D航线
      if (this.use3DFlightPath) {
        if (this.currentAirline.waypoints.length >= 2) {
          this.create3DFlightPath()
        } else {
          // 航点不足，删除3D航线
          if (this.threeFlightPathManager) {
            this.threeFlightPathManager.removeFlightPath('current_airline')
          }
        }
      }

      // 标记数据已变更
      this.markDataChanged()

      console.log('删除航点:', waypointId)
    },

    // 更新所有航点标记
    updateWaypointMarkers() {
      if (!this.currentAirline) return

      this.currentAirline.waypoints.forEach((waypoint, index) => {
        const marker = this.waypointMarkers[waypoint.id]
        if (marker) {
          const content = marker.getContent()
          const numberSpan = content.querySelector('.waypoint-number')
          if (numberSpan) {
            numberSpan.textContent = index + 1
          }
        }
      })
    },

    // 清空所有航点
    clearAllWaypoints() {
      if (!this.currentAirline) return

      this.$confirm('确定要清空所有航点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 记录操作历史
        this.recordAction('clear_waypoints', { waypoints: [...this.currentAirline.waypoints] })

        // 清除所有2D标记
        Object.values(this.waypointMarkers).forEach(marker => {
          marker.setMap(null)
        })
        this.waypointMarkers = {}

        // 清除所有3D航点
        if (this.use3DWaypoints && this.threeWaypointManager) {
          try {
            this.currentAirline.waypoints.forEach(waypoint => {
              this.threeWaypointManager.removeWaypoint(waypoint.id)
            })
            console.log('所有3D航点已清空')
          } catch (error) {
            console.error('清空3D航点失败:', error)
          }
        }

        // 清除所有连线
        this.waypointLines.forEach(line => {
          line.setMap(null)
        })
        this.waypointLines = []

        // 清除所有距离标签
        this.distanceLabels.forEach(label => {
          label.setMap(null)
        })
        this.distanceLabels = []

        // 清空航点数组
        this.currentAirline.waypoints = []

        // 清空3D航线
        if (this.use3DFlightPath && this.threeFlightPathManager) {
          this.threeFlightPathManager.removeFlightPath('current_airline')
        }

        // 标记数据已变更
        this.markDataChanged()

        this.$message.success('已清空所有航点')
      })
    },

    // 撤销操作
    undoLastAction() {
      if (!this.canUndo) return

      const action = this.actionHistory[this.actionHistoryIndex]
      this.actionHistoryIndex--

      switch (action.type) {
        case 'add_waypoint':
          this.undoAddWaypoint(action.data)
          break
        case 'delete_waypoint':
          this.undoDeleteWaypoint(action.data)
          break
        case 'clear_waypoints':
          this.undoClearWaypoints(action.data)
          break
        case 'move_waypoint':
          this.undoMoveWaypoint(action.data)
          break
      }
    },

    // 重做操作
    redoLastAction() {
      if (!this.canRedo) return

      this.actionHistoryIndex++
      const action = this.actionHistory[this.actionHistoryIndex]

      switch (action.type) {
        case 'add_waypoint':
          this.redoAddWaypoint(action.data)
          break
        case 'delete_waypoint':
          this.redoDeleteWaypoint(action.data)
          break
        case 'clear_waypoints':
          this.redoClearWaypoints(action.data)
          break
        case 'move_waypoint':
          this.redoMoveWaypoint(action.data)
          break
      }
    },

    // 记录操作历史
    recordAction(type, data) {
      // 清除当前位置之后的历史记录
      this.actionHistory = this.actionHistory.slice(0, this.actionHistoryIndex + 1)

      // 添加新的操作记录
      this.actionHistory.push({
        type,
        data,
        timestamp: Date.now()
      })

      this.actionHistoryIndex = this.actionHistory.length - 1

      // 限制历史记录数量
      if (this.actionHistory.length > 50) {
        this.actionHistory.shift()
        this.actionHistoryIndex--
      }
    },

    // 撤销添加航点
    undoAddWaypoint(data) {
      const waypoint = data.waypoint
      const index = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
      if (index !== -1) {
        this.currentAirline.waypoints.splice(index, 1)

        if (this.waypointMarkers[waypoint.id]) {
          this.waypointMarkers[waypoint.id].setMap(null)
          delete this.waypointMarkers[waypoint.id]
        }

        this.updateWaypointMarkers()
        this.updateWaypointLines()
      }
    },

    // 撤销删除航点
    undoDeleteWaypoint(data) {
      const { waypoint, index } = data
      this.currentAirline.waypoints.splice(index, 0, waypoint)
      this.createWaypointMarker(waypoint)

      // 如果启用了3D航点且管理器已初始化，重新创建3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          this.threeWaypointManager.addWaypoint(
            waypoint.id,
            waypoint.longitude,
            waypoint.latitude,
            waypoint.height,
            {
              speed: waypoint.speed,
              actions: waypoint.actions
            }
          )
          console.log('撤销删除3D航点成功:', waypoint.id)
        } catch (error) {
          console.error('撤销删除3D航点失败:', error)
        }
      }

      this.updateWaypointMarkers()
      this.updateWaypointLines()
    },

    // 撤销清空航点
    undoClearWaypoints(data) {
      this.currentAirline.waypoints = [...data.waypoints]
      this.currentAirline.waypoints.forEach(waypoint => {
        this.createWaypointMarker(waypoint)

        // 如果启用了3D航点且管理器已初始化，重新创建3D航点
        if (this.use3DWaypoints && this.threeWaypointManager) {
          try {
            this.threeWaypointManager.addWaypoint(
              waypoint.id,
              waypoint.longitude,
              waypoint.latitude,
              waypoint.height,
              {
                speed: waypoint.speed,
                actions: waypoint.actions
              }
            )
          } catch (error) {
            console.error('撤销清空时重建3D航点失败:', error)
          }
        }
      })
      this.updateWaypointLines()
      console.log('撤销清空航点成功，已恢复', data.waypoints.length, '个航点')
    },

    // 撤销移动航点
    undoMoveWaypoint(data) {
      const { waypointId, oldPosition } = data
      const waypoint = this.currentAirline.waypoints.find(w => w.id === waypointId)
      if (waypoint) {
        waypoint.longitude = oldPosition.longitude
        waypoint.latitude = oldPosition.latitude

        // 更新标记位置
        const marker = this.waypointMarkers[waypointId]
        if (marker) {
          const gcj02 = this.wgs84ToGcj02(waypoint.longitude, waypoint.latitude)
          marker.setPosition([gcj02.lng, gcj02.lat])
        }

        this.updateWaypointLines()
      }
    },

    // 重做添加航点
    redoAddWaypoint(data) {
      const waypoint = data.waypoint
      this.currentAirline.waypoints.push(waypoint)
      this.createWaypointMarker(waypoint)

      // 如果启用了3D航点且管理器已初始化，重新创建3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          this.threeWaypointManager.addWaypoint(
            waypoint.id,
            waypoint.longitude,
            waypoint.latitude,
            waypoint.height,
            {
              speed: waypoint.speed,
              actions: waypoint.actions
            }
          )
          console.log('重做添加3D航点成功:', waypoint.id)
        } catch (error) {
          console.error('重做添加3D航点失败:', error)
        }
      }

      this.updateWaypointLines()
    },

    // 重做删除航点
    redoDeleteWaypoint(data) {
      const waypoint = data.waypoint
      const index = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
      if (index !== -1) {
        this.currentAirline.waypoints.splice(index, 1)

        if (this.waypointMarkers[waypoint.id]) {
          this.waypointMarkers[waypoint.id].setMap(null)
          delete this.waypointMarkers[waypoint.id]
        }

        this.updateWaypointMarkers()
        this.updateWaypointLines()
      }
    },

    // 重做清空航点
    redoClearWaypoints() {
      // 清除所有2D标记
      Object.values(this.waypointMarkers).forEach(marker => {
        marker.setMap(null)
      })
      this.waypointMarkers = {}

      // 清除所有3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        try {
          this.currentAirline.waypoints.forEach(waypoint => {
            this.threeWaypointManager.removeWaypoint(waypoint.id)
          })
          console.log('重做清空：所有3D航点已清空')
        } catch (error) {
          console.error('重做清空3D航点失败:', error)
        }
      }

      this.waypointLines.forEach(line => {
        line.setMap(null)
      })
      this.waypointLines = []

      this.distanceLabels.forEach(label => {
        label.setMap(null)
      })
      this.distanceLabels = []

      this.currentAirline.waypoints = []
    },

    // 重做移动航点
    redoMoveWaypoint(data) {
      const { waypointId, newPosition } = data
      const waypoint = this.currentAirline.waypoints.find(w => w.id === waypointId)
      if (waypoint) {
        waypoint.longitude = newPosition.longitude
        waypoint.latitude = newPosition.latitude

        // 更新标记位置
        const marker = this.waypointMarkers[waypointId]
        if (marker) {
          const gcj02 = this.wgs84ToGcj02(waypoint.longitude, waypoint.latitude)
          marker.setPosition([gcj02.lng, gcj02.lat])
        }

        this.updateWaypointLines()
      }
    },

    // 保存当前航线
    async saveCurrentAirline() {
      if (!this.currentAirline) {
        this.$message.warning('没有航线数据可保存')
        return
      }

      // 验证文件名
      if (!this.currentAirline.fileName || this.currentAirline.fileName.trim() === '') {
        this.$message.warning('请填写航线名称')
        return
      }

      if (this.currentAirline.fileName.length > 15) {
        this.$message.warning('航线名称不能超过15个字符')
        return
      }

      // 验证是否有航点
      if (!this.currentAirline.waypoints || this.currentAirline.waypoints.length === 0) {
        this.$message.warning('请至少添加一个航点')
        return
      }

      // 验证是否有起飞点
      if (!this.takeoffPoint) {
        this.$message.warning('请设置起飞点')
        return
      }

      try {
        // 更新时间
        this.currentAirline.updateTime = new Date()

        // 转换为test.html格式的数据
        const waylineData = this.convertToWaylineFormat()
        waylineData.fileName = this.currentAirline.fileName;
        // 调试日志：打印转换后的数据
        console.log('转换后的wayline数据:', JSON.stringify(waylineData, null, 2))

        // 调用createWayline接口
        const response = await createWayline(waylineData)

        if (response.code === 200 || response.success) {
          this.$message.success('航线保存成功')

          // 标记数据已保存
          this.hasUnsavedChanges = false

          // 保存成功后关闭当前页面，返回上一级页面并刷新
          this.goBackAndRefresh()
        } else {
          throw new Error(response.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存航线数据失败:', error)
        this.$message.error('保存失败: ' + (error.message || error))
      }
    },

    // 保存成功后返回上一级页面并刷新
    goBackAndRefresh() {
      // 恢复sidebar状态
      this.restoreSidebar()

      // 使用浏览器后退功能返回上一级页面
      this.$router.go(-1)

      // 延迟一点时间确保页面已经返回，然后刷新页面
      setTimeout(() => {
        // 刷新当前页面（即上一级页面）
        window.location.reload()
      }, 100)
    },

    // 转换为wayline格式
    convertToWaylineFormat() {
      if (!this.currentAirline) {
        throw new Error('没有航线数据')
      }

      // 获取航线类型映射
      const templateTypeMap = {
        'waypoint': 'waypoint',
        'normal': 'waypoint',
        'mapping': 'mapping2d',
        'oblique': 'mapping3d',
        'strip': 'mappingStrip'
      }

      // 转换航点数据
      const routePointList = this.currentAirline.waypoints.map((waypoint, index) => {
        const routePoint = {
          routePointIndex: index,
          latitude: waypoint.latitude,
          longitude: waypoint.longitude,
          altitude: waypoint.height || this.currentAirline.defaultHeight || 100,
          heading: waypoint.heading || 0,
          gimbalPitch: waypoint.gimbalPitch || 0,
          waypointTurnReq: {
            waypointTurnMode: waypoint.turnMode || ''
          },
          actions: []
        }

        // 转换动作数据
        if (waypoint.actions && waypoint.actions.length > 0) {
          routePoint.actions = waypoint.actions.map((action, actionIndex) => {
            const convertedAction = {
              actionIndex: actionIndex,
              actionType: this.convertActionType(action.type)
            }

            // 根据动作类型添加参数
            switch (action.type) {
              case 'photo':
              case 'takePhoto':
                convertedAction.useGlobalImageFormat = action.useGlobalImageFormat || action.params?.useGlobalImageFormat || 0
                convertedAction.imageFormat = action.imageFormat || action.params?.imageFormat || 'wide,zoom,ir,visable'
                break
              case 'hover':
                convertedAction.hoverTime = action.hoverTime || action.params?.duration || 1
                break
              case 'drone_yaw':
              case 'rotateYaw':
                convertedAction.aircraftHeading = action.aircraftHeading || action.params?.angle || 0
                break
              case 'gimbal_yaw':
              case 'gimbalRotate':
                convertedAction.gimbalYawRotateAngle = action.gimbalYawRotateAngle || action.params?.yaw || 0
                convertedAction.gimbalPitchRotateAngle = action.gimbalPitchRotateAngle || action.params?.pitch || 0
                break
              case 'zoom':
                convertedAction.zoom = action.zoom || action.params?.factor || 1
                break
            }

            return convertedAction
          })
        }

        return routePoint
      })

      // 构建完整的wayline数据
      const waylineData = {
        templateType: templateTypeMap[this.currentAirline.type] || 'waypoint',
        droneType: 67,
        subDroneType: 0,
        payloadType: 52,
        payloadPosition: 0,
        imageFormat: 'wide,zoom,ir,visable',
        finishAction: this.convertFinishAction(this.currentAirline.finishAction),
        exitOnRcLostAction: 'goBack',
        globalHeight: this.currentAirline.defaultHeight || 100,
        autoFlightSpeed: this.currentAirline.defaultSpeed || 10,
        gimbalPitchMode: this.currentAirline.gimbalPitchMode || 'manual',
        waypointHeadingReq: {
          waypointHeadingMode: this.currentAirline.headingMode === 'wayline' ? 'auto' : 'manual',
          waypointHeadingAngle: 0
        },
        waypointTurnReq: {
          waypointTurnMode: this.currentAirline.waypointType === 'straight_hover' ? 'toPointAndStopWithDiscontinuityCurvature' : 'coordinateTurn'
        },
        takeOffRefPoint: this.takeoffPoint ? `${this.takeoffPoint.longitude.toFixed(6)},${this.takeoffPoint.latitude.toFixed(6)}` : '',
        mappingTypeReq: {
          type: 'mapping',
          overlapRate: 75,
          sideOverlapRate: 75,
          photoInterval: 2
        },
        routePointList: routePointList
      }

      return waylineData
    },

    // 转换动作类型
    convertActionType(actionType) {
      const actionTypeMap = {
        // flightairline.vue中的动作类型映射到test.html格式
        'photo': 'takePhoto',
        'video_start': 'startRecord',
        'video_stop': 'stopRecord',
        'hover': 'hover',
        'drone_yaw': 'rotateYaw',
        'gimbal_yaw': 'gimbalRotate',
        'zoom': 'zoom',
        'panorama': 'panoShot',
        // 直接映射的类型
        'takePhoto': 'takePhoto',
        'startRecord': 'startRecord',
        'stopRecord': 'stopRecord',
        'rotateYaw': 'rotateYaw',
        'gimbalRotate': 'gimbalRotate',
        'focus': 'focus',
        'customDirName': 'customDirName',
        'gimbalEvenlyRotate': 'gimbalEvenlyRotate',
        'orientedShoot': 'orientedShoot',
        'panoShot': 'panoShot',
        'recordPointCloud': 'recordPointCloud'
      }
      return actionTypeMap[actionType] || actionType
    },

    // 转换结束动作
    convertFinishAction(finishAction) {
      const finishActionMap = {
        'return_home': 'goHome',
        'hover': 'noAction',
        'land': 'autoLand',
        'goto_first': 'gotoFirstWaypoint'
      }
      return finishActionMap[finishAction] || 'goHome'
    },

    // 加载航线详情
    async loadWaylineDetail(waylineId) {
      try {
        console.log('加载航线详情，ID:', waylineId)
        const response = await getWaylineDetail(waylineId)

        if (response.code === 200 && response.data) {
          const waylineData = response.data
          console.log('获取到的航线详情:', waylineData)

          // 转换航线数据格式
          this.convertWaylineDataToAirline(waylineData)

          this.$message.success('航线详情加载成功')
        } else {
          throw new Error(response.msg || '获取航线详情失败')
        }
      } catch (error) {
        console.error('加载航线详情失败:', error)
        this.$message.error('加载航线详情失败: ' + (error.message || error))

        // 加载失败时使用默认数据
        this.loadCurrentAirline()
      }
    },

    // 将航线详情数据转换为界面格式
    convertWaylineDataToAirline(waylineData) {
      try {
        // 更新基本航线信息
        this.currentAirline.fileName = waylineData.fileName
        this.currentAirline.defaultHeight = waylineData.globalHeight || 127
        this.currentAirline.defaultSpeed = waylineData.autoFlightSpeed || 10

        // 转换结束动作
        const finishActionMap = {
          'goHome': 'return_home',
          'noAction': 'hover',
          'autoLand': 'land',
          'gotoFirstWaypoint': 'goto_first'
        }
        this.currentAirline.finishAction = finishActionMap[waylineData.finishAction] || 'return_home'

        // 处理起飞点数据
        if (waylineData.takeOffRefPoint) {
          const coords = waylineData.takeOffRefPoint.split(',')
          if (coords.length === 2) {
            const lng = parseFloat(coords[0])
            const lat = parseFloat(coords[1])
            if (!isNaN(lng) && !isNaN(lat)) {
              this.takeoffPoint = {
                longitude: lng,
                latitude: lat
              }
              console.log('设置起飞点:', this.takeoffPoint)
            }
          }
        }

        // 清空现有航点
        this.currentAirline.waypoints = []

        // 转换航点数据
        if (waylineData.routePointList && waylineData.routePointList.length > 0) {
          waylineData.routePointList.forEach((routePoint, index) => {
            const waypoint = {
              id: `waypoint_${index + 1}`,
              name: `${routePoint.longitude.toFixed(6)},${routePoint.latitude.toFixed(6)}`,
              longitude: routePoint.longitude,
              latitude: routePoint.latitude,
              height: routePoint.altitude || this.currentAirline.defaultHeight,
              speed: routePoint.speed || this.currentAirline.defaultSpeed,
              heading: routePoint.heading || 0,
              gimbalPitch: routePoint.gimbalPitch || 0,
              actions: []
            }

            // 转换动作数据
            if (routePoint.actions && routePoint.actions.length > 0) {
              routePoint.actions.forEach((action, actionIndex) => {
                const convertedAction = this.convertApiActionToUIAction(action, actionIndex + 1)
                if (convertedAction) {
                  waypoint.actions.push(convertedAction)
                }
              })
            }

            this.currentAirline.waypoints.push(waypoint)
          })
        }

        console.log('转换后的航线数据:', this.currentAirline)
        console.log('起飞点数据:', this.takeoffPoint)

        // 更新页面标题
        this.updatePageTitle()

        // 延迟更新地图，确保地图已经初始化
        this.$nextTick(() => {
          this.updateMapWaypoints()
          // 如果有起飞点，也要显示起飞点
          if (this.takeoffPoint) {
            this.createTakeoffPointMarker()
          }
        })

      } catch (error) {
        console.error('转换航线数据失败:', error)
        this.$message.error('数据转换失败: ' + (error.message || error))
      }
    },

    // 将API动作格式转换为UI动作格式
    convertApiActionToUIAction(apiAction, actionId) {
      const actionTypeMap = {
        'takePhoto': 'takePhoto',
        'startRecord': 'startRecord',
        'stopRecord': 'stopRecord',
        'focus': 'focus',
        'zoom': 'zoom',
        'customDirName': 'customDirName',
        'gimbalRotate': 'gimbalRotate',
        'rotateYaw': 'rotateYaw',
        'hover': 'hover',
        'gimbalEvenlyRotate': 'gimbalEvenlyRotate',
        'orientedShoot': 'orientedShoot',
        'panoShot': 'panoShot',
        'recordPointCloud': 'recordPointCloud'
      }

      const actionType = actionTypeMap[apiAction.actionType]
      if (!actionType) {
        console.warn('未知的动作类型:', apiAction.actionType)
        return null
      }

      const uiAction = {
        id: actionId,
        type: actionType,
        params: {}
      }

      // 根据动作类型设置参数
      switch (actionType) {
        case 'gimbal_pitch':
        case 'gimbal_yaw':
          uiAction.params = {
            angle: apiAction.gimbalAngle || 0,
            time: apiAction.gimbalTime || 0
          }
          break
        case 'zoom':
          uiAction.params = {
            factor: apiAction.zoomFactor || 1
          }
          break
        case 'hover':
          uiAction.params = {
            time: apiAction.hoverTime || 3
          }
          break
        default:
          // photo, video_start, video_end, panorama 等动作无需参数
          break
      }

      return uiAction
    },

    // 更新页面标题
    updatePageTitle() {
      const waylineId = this.$route.params.waylineId || this.$route.query.id
      const fileName = this.currentAirline.fileName

      if (waylineId && fileName) {
        // 更新当前路由的 meta 信息
        const newTitle = `编辑航线 - ${fileName}`

        // 更新 TagsView 中的标题
        const currentRoute = {
          ...this.$route,
          meta: {
            ...this.$route.meta,
            title: newTitle
          }
        }

        this.$store.dispatch('tagsView/updateVisitedView', currentRoute)

        // 更新浏览器标题
        document.title = newTitle

        console.log('更新页面标题:', newTitle)
      }
    },

    // 更新地图航点显示
    updateMapWaypoints() {
      try {
        // 清除现有的航点标记（不弹出确认对话框）
        this.clearMapMarkers()

        // 重新添加航点到地图
        if (this.currentAirline.waypoints && this.currentAirline.waypoints.length > 0) {
          if (this.use3DWaypoints && this.threeWaypointManager) {
            // 使用3D航点管理器
            this.currentAirline.waypoints.forEach((waypoint, index) => {
              this.threeWaypointManager.addWaypoint(
                waypoint.id,
                waypoint.longitude,
                waypoint.latitude,
                waypoint.height || this.currentAirline.defaultHeight || 100,
                { index: index + 1 }
              )
            })
          } else {
            // 使用传统2D标记
            this.currentAirline.waypoints.forEach((waypoint) => {
              this.createWaypointMarker(waypoint)
            })
          }

          // 更新航点连线
          this.updateWaypointLines()

          // 如果有航点，将地图中心移动到第一个航点
          if (this.currentAirline.waypoints.length > 0) {
            const firstWaypoint = this.currentAirline.waypoints[0]
            if (firstWaypoint.longitude && firstWaypoint.latitude) {
              // 转换为GCJ02坐标系用于地图显示
              const gcj02 = this.wgs84ToGcj02(firstWaypoint.longitude, firstWaypoint.latitude)
              this.$refs.amap.map.setCenter([gcj02.lng, gcj02.lat])
            }
          }
        }
      } catch (error) {
        console.error('更新地图航点失败:', error)
      }
    },

    // 加载当前航线
    loadCurrentAirline() {
      // 不再从 localStorage 加载数据，使用默认数据
      console.log('使用默认航线数据')
    },

    // 清除地图标记
    clearMapMarkers() {
      // 清除3D航点
      if (this.use3DWaypoints && this.threeWaypointManager) {
        this.threeWaypointManager.getAllWaypoints().forEach(waypoint => {
          this.threeWaypointManager.removeWaypoint(waypoint.id)
        })
      }

      // 清除传统2D航点标记
      Object.values(this.waypointMarkers).forEach(marker => {
        marker.setMap(null)
      })
      this.waypointMarkers = {}

      // 清除连线
      this.waypointLines.forEach(line => {
        line.setMap(null)
      })
      this.waypointLines = []

      // 清除距离标签
      this.distanceLabels.forEach(label => {
        label.setMap(null)
      })
      this.distanceLabels = []
    },



    // 航点排序重新排列
    onWaypointsReorder() {
      this.updateWaypointMarkers()
      this.updateWaypointLines()
    },

    // 重新分配动作ID
    reassignActionIds(waypoint) {
      waypoint.actions.forEach((action, index) => {
        action.id = index + 1
      })
      console.log('重新分配动作ID:', waypoint.actions.map(a => ({ type: a.type, id: a.id })))
    },

    // 动作重新排序
    onActionsReorder(waypoint) {
      console.log('动作重新排序:', waypoint.id)
      this.reassignActionIds(waypoint)
    },

    // 优化航点顺序
    optimizeWaypoints() {
      if (!this.currentAirline || this.currentAirline.waypoints.length < 2) {
        this.$message.warning('至少需要2个航点才能进行优化')
        return
      }

      this.$confirm('确定要优化航点顺序吗？这将重新排列航点以获得最短路径。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        // 简单的最近邻算法优化路径
        const waypoints = [...this.currentAirline.waypoints]
        let optimized = []
        let startPoint = null

        // 如果有起飞点，将起飞点作为起始点
        if (this.takeoffPoint) {
          startPoint = this.takeoffPoint
          console.log('使用起飞点作为优化起始点:', startPoint)
        } else {
          // 没有起飞点时，使用第一个航点作为起始点
          startPoint = waypoints[0]
          optimized = [waypoints[0]]
          waypoints.splice(0, 1)
          console.log('使用第一个航点作为优化起始点:', startPoint)
        }

        // 最近邻算法：依次找到距离当前点最近的下一个点
        while (waypoints.length > 0) {
          const current = optimized.length > 0 ? optimized[optimized.length - 1] : startPoint
          let nearestIndex = 0
          let nearestDistance = this.calculateDistance(current, waypoints[0])

          // 找到距离当前点最近的航点
          for (let i = 1; i < waypoints.length; i++) {
            const distance = this.calculateDistance(current, waypoints[i])
            if (distance < nearestDistance) {
              nearestDistance = distance
              nearestIndex = i
            }
          }

          // 将最近的航点添加到优化路径中
          optimized.push(waypoints[nearestIndex])
          waypoints.splice(nearestIndex, 1)
        }

        this.currentAirline.waypoints = optimized
        this.updateWaypointMarkers()
        this.updateWaypointLines()

        const message = this.takeoffPoint
          ? '航点顺序优化完成（基于起飞点）'
          : '航点顺序优化完成'
        this.$message.success(message)
      })
    },

    // 编辑航点动作
    editWaypointActions(waypointId) {
      const waypoint = this.currentAirline.waypoints.find(w => w.id === waypointId)
      if (!waypoint) return

      this.currentEditingWaypoint = { ...waypoint }
      this.newActionType = ''
      this.waypointActionsDialogVisible = true
    },

    // 获取航点索引
    getWaypointIndex(waypointId) {
      return this.currentAirline.waypoints.findIndex(w => w.id === waypointId)
    },

    // 添加动作
    addAction() {
      if (!this.newActionType || !this.currentEditingWaypoint) return

      const action = {
        id: this.generateId(), // 添加唯一ID
        type: this.newActionType,
        params: this.getDefaultActionParams(this.newActionType)
      }

      this.currentEditingWaypoint.actions.push(action)
      this.newActionType = ''
    },

    // 获取默认动作参数
    getDefaultActionParams(actionType) {
      switch (actionType) {
        case 'hover':
          return { duration: 3 }
        case 'gimbal':
          return { pitch: -45 }
        case 'wait':
          return { duration: 5 }
        default:
          return {}
      }
    },

    // 删除动作
    removeAction(index) {
      this.currentEditingWaypoint.actions.splice(index, 1)
      this.reassignActionIds(this.currentEditingWaypoint)
    },

    // 保存航点动作
    saveWaypointActions() {
      const waypoint = this.currentAirline.waypoints.find(w => w.id === this.currentEditingWaypoint.id)
      if (waypoint) {
        Object.assign(waypoint, this.currentEditingWaypoint)
        this.waypointActionsDialogVisible = false

      }
    },

    // 调整速度
    adjustSpeed(delta) {
      const newSpeed = this.currentAirline.defaultSpeed + delta
      if (newSpeed >= 1 && newSpeed <= 15) {
        this.currentAirline.defaultSpeed = newSpeed
      }
    },

    // 调整起飞速度
    adjustTakeoffSpeed(delta) {
      const newSpeed = this.currentAirline.takeoffSpeed + delta
      if (newSpeed >= 1 && newSpeed <= 15) {
        this.currentAirline.takeoffSpeed = newSpeed
      }
    },

    // 调整高度
    adjustHeight(delta) {
      const newHeight = this.currentAirline.defaultHeight + delta
      if (newHeight >= 10 && newHeight <= 500) {
        this.currentAirline.defaultHeight = newHeight
      }
    },

    // 切换全局设置显示
    toggleGlobalSettings() {
      this.showGlobalSettings = !this.showGlobalSettings
    },

    // 切换航点列表显示
    toggleWaypointsList() {
      this.showWaypointsList = !this.showWaypointsList
    },

    // 切换起飞点设置模式
    toggleTakeoffPointSetting() {
      this.isSettingTakeoffPoint = !this.isSettingTakeoffPoint

      if (this.isSettingTakeoffPoint) {
        // 进入设置模式，创建跟随鼠标的标记
        this.createMouseFollowMarker()
        // 改变鼠标样式
        if (this.$refs.amap && this.$refs.amap.map) {
          const mapContainer = this.$refs.amap.map.getContainer()
          mapContainer.style.cursor = 'crosshair'
        }
      } else {
        // 退出设置模式，移除跟随鼠标的标记
        this.removeMouseFollowMarker()
        // 恢复鼠标样式
        if (this.$refs.amap && this.$refs.amap.map) {
          const mapContainer = this.$refs.amap.map.getContainer()
          mapContainer.style.cursor = 'default'
        }
      }
    },

    // 创建跟随鼠标的标记
    createMouseFollowMarker() {
      if (!this.$refs.amap || !this.$refs.amap.map) return

      // 创建自定义HTML内容
      const markerContent = document.createElement('div')
      markerContent.className = 'takeoff-point-marker mouse-follow'
      markerContent.style.pointerEvents = 'none' // 让点击事件穿透到地图
      markerContent.style.position = 'absolute'
      markerContent.style.zIndex = '1000'
      markerContent.innerHTML = `
        <?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1751249917899" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1389" xmlns:xlink="http://www.w3.org/1999/xlink" width="32" height="32" style="pointer-events: none;"><path d="M512 84.1728l215.1936 239.104H583.68v153.088H440.32v-153.088H296.8576L512 84.1216z m423.7312 578.048c0 105.4208-189.696 190.8736-423.68 190.8736-234.0352 0-423.7312-85.504-423.7312-190.8736 0-68.8128 80.7936-151.6032 202.0352-200.8064C120.2176 501.6064 0 595.456 0 688.2304c0 138.9568 229.2224 251.5968 512 251.5968s512-112.64 512-251.5968c0-92.7232-120.1664-186.624-290.2528-226.816 121.1904 49.2544 201.984 131.9936 201.984 200.8064z m-516.0448-34.7136l12.8-69.4272H361.984l-35.328 190.8736h70.656l16.0256-86.7328h197.376l16.0768 86.7328h70.656l-35.328-190.8736h-70.656l12.8 69.4272H419.7376z" fill="#5b8ce8" p-id="1390"></path></svg>
      `

      this.mouseFollowMarker = new AMap.Marker({
        position: [0, 0], // 初始位置
        map: this.$refs.amap.map,
        content: markerContent,
        offset: new AMap.Pixel(-16, -16),
        zIndex: 1000,
        clickable: false // 标记不可点击
      })
    },

    // 移除跟随鼠标的标记
    removeMouseFollowMarker() {
      if (this.mouseFollowMarker) {
        this.mouseFollowMarker.setMap(null)
        this.mouseFollowMarker = null
      }
    },

    // 设置起飞点
    setTakeoffPoint(lng, lat) {
      // 转换为WGS84坐标
      const wgs84 = this.gcj02ToWgs84(lng, lat)

      this.takeoffPoint = {
        longitude: wgs84.lng,
        latitude: wgs84.lat,
        createTime: new Date()
      }

      // 创建起飞点标记
      this.createTakeoffPointMarker()

      // 更新连线
      this.updateWaypointLines()

      // 退出设置模式
      this.isSettingTakeoffPoint = false
      this.removeMouseFollowMarker()

      // 恢复鼠标样式
      if (this.$refs.amap && this.$refs.amap.map) {
        const mapContainer = this.$refs.amap.map.getContainer()
        mapContainer.style.cursor = 'default'
      }

      this.$message.success('起飞点设置成功')
    },

    // 创建起飞点标记
    createTakeoffPointMarker() {
      if (!this.takeoffPoint || !this.$refs.amap || !this.$refs.amap.map) return

      // 清除现有起飞点标记
      if (this.takeoffPointMarker) {
        this.takeoffPointMarker.setMap(null)
      }

      const gcj02 = this.wgs84ToGcj02(this.takeoffPoint.longitude, this.takeoffPoint.latitude)

      // 创建自定义HTML内容
      const markerContent = document.createElement('div')
      markerContent.className = 'takeoff-point-marker fixed'
      markerContent.style.pointerEvents = 'auto' // 固定的起飞点可以点击
      markerContent.innerHTML = `
        <?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1751249917899" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1389" xmlns:xlink="http://www.w3.org/1999/xlink" width="32" height="32"><path d="M512 84.1728l215.1936 239.104H583.68v153.088H440.32v-153.088H296.8576L512 84.1216z m423.7312 578.048c0 105.4208-189.696 190.8736-423.68 190.8736-234.0352 0-423.7312-85.504-423.7312-190.8736 0-68.8128 80.7936-151.6032 202.0352-200.8064C120.2176 501.6064 0 595.456 0 688.2304c0 138.9568 229.2224 251.5968 512 251.5968s512-112.64 512-251.5968c0-92.7232-120.1664-186.624-290.2528-226.816 121.1904 49.2544 201.984 131.9936 201.984 200.8064z m-516.0448-34.7136l12.8-69.4272H361.984l-35.328 190.8736h70.656l16.0256-86.7328h197.376l16.0768 86.7328h70.656l-35.328-190.8736h-70.656l12.8 69.4272H419.7376z" fill="#5b8ce8" p-id="1390"></path></svg>
      `

      this.takeoffPointMarker = new AMap.Marker({
        position: [gcj02.lng, gcj02.lat],
        map: this.$refs.amap.map,
        content: markerContent,
        offset: new AMap.Pixel(-20, -20),
        title: '起飞点',
        zIndex: 999
      })
    },

    // 清除起飞点
    clearTakeoffPoint() {
      this.takeoffPoint = null

      // 清除起飞点标记
      if (this.takeoffPointMarker) {
        this.takeoffPointMarker.setMap(null)
        this.takeoffPointMarker = null
      }

      // 更新连线
      this.updateWaypointLines()

      this.$message.success('起飞点已清除')
    },

    // 云台控制方法
    handleGimbalYaw() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('gimbalRotate')
      } else {
        console.log('云台偏航控制')
        this.$message.info('云台偏航控制')
      }
    },

    handleTakePhoto() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('takePhoto')
      } else {
        console.log('拍照')
        this.$message.success('拍照指令已发送')
      }
    },

    handleZoom() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('zoom')
      } else {
        console.log('变焦控制')
        this.$message.info('变焦控制')
      }
    },

    handlePanorama() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('panoShot')
      } else {
        console.log('全景照片')
        this.$message.info('全景照片拍摄')
      }
    },

    // 录像控制方法
    handleStartRecording() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('startRecord')
      } else {
        this.isRecording = true
        console.log('开始录像')
        this.$message.success('开始录像')
      }
    },

    handleStopRecording() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('stopRecord')
      } else {
        this.isRecording = false
        console.log('停止录像')
        this.$message.success('停止录像')
      }
    },

    // 飞行控制方法
    handleDroneYaw() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('rotateYaw')
      } else {
        console.log('无人机偏航控制')
        this.$message.info('无人机偏航控制')
      }
    },

    handleHover() {
      if (this.selectedWaypointId) {
        this.addActionToSelectedWaypoint('hover')
      } else {
        console.log('悬停')
        this.$message.info('无人机悬停')
      }
    },

    // 切换3D地图视角
    toggle3DMap() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }

      const map = this.$refs.amap.map;

      if (this.is3DMode) {
        // 切换到2D视角（俯仰角0度，垂直向下看）
        try {
          // 平滑过渡到2D视角
          if (map.setPitch) {
            map.setPitch(0, true); // 第二个参数为true表示使用动画
          }
          if (map.setRotation) {
            map.setRotation(0, true);
          }

          this.is3DMode = false;
          this.$message.info('已切换到2D视角');

        
        } catch (error) {
          console.error('切换到2D视角失败:', error);
          this.$message.error('切换到2D视角失败');
        }
      } else {
        // 切换到3D视角（倾斜角度，立体效果）
        try {
          // 平滑过渡到3D视角
          if (map.setPitch) {
            map.setPitch(50, true); // 设置俯仰角50度，带动画
          } else {
            console.warn('地图不支持setPitch方法');
            this.$message.warning('当前地图版本不支持3D视角功能');
            return;
          }

          if (map.setRotation) {
            map.setRotation(-15, true); // 设置旋转角度，带动画
          }

          this.is3DMode = true;
          this.$message.success('已切换到3D视角');

          // 加载3D地图控制插件
          this.load3DControls();
        } catch (error) {
          console.error('切换到3D视角失败:', error);
          this.$message.error('3D视角功能暂不可用');
          this.is3DMode = false;
        }
      }
    },

    // 重新创建支持3D的地图
    recreateMapWith3D() {
      try {
        if (!this.$refs.amap) return;

        // 保存当前地图状态
        const currentMap = this.$refs.amap.map;
        const currentCenter = currentMap ? currentMap.getCenter() : [113.014701, 28.194985];
        const currentZoom = currentMap ? currentMap.getZoom() : 13;

        // 销毁当前地图
        if (currentMap) {
          currentMap.destroy();
        }

        // 创建新的3D地图
        const map3D = new AMap.Map('map', {
          center: currentCenter,
          zoom: currentZoom,
          viewMode: '3D',
          pitch: 50,
          rotation: -15,
          mapStyle: 'amap://styles/normal',
          features: ['bg', 'point', 'road', 'building'],
          showLabel: true,
          defaultCursor: 'pointer',
          rotateEnable: true, // 启用旋转功能
    keyboardEnable: false, // 禁用键盘控制
    dragEnable: false, // 禁用拖拽平移
    doubleClickZoom: false, // 禁用双击缩放
    scrollWheel: true, // 禁用滚轮缩放
    pitchEnable:true,//地图倾斜角上下倾斜角
          animateEnable: true,
          jogEnable: true,
          
          resizeEnable: true,
          showIndoorMap: false,
          expandZoomRange: true,
          zooms: [3, 20]
        });

        // 更新引用
        this.$refs.amap.map = map3D;

        // 重新绑定事件
        this.rebindMapEvents(map3D);

        // 重新创建航点标记
        this.recreateWaypointMarkers();

        this.is3DMode = true;
        this.$message.success('已切换到3D地图');

        // 加载3D地图控制插件
        this.load3DControls();

      } catch (error) {
        console.error('重新创建3D地图失败:', error);
        this.$message.error('3D地图功能不可用，请检查地图API版本');
      }
    },

    // 重新绑定地图事件
    rebindMapEvents(map) {
      // 添加地图点击事件
      map.on('click', this.onMapClick);

      // 添加地图右键点击事件
      map.on('rightclick', this.onMapRightClick);

      // 添加鼠标移动事件
      map.on('mousemove', this.onMouseMove);

      // 监听地图缩放事件
      map.on('zoomend', () => {
        this.mapZoomLevel = map.getZoom();
      });

      console.log('地图事件重新绑定完成');
    },

    // 重新创建航点标记
    recreateWaypointMarkers() {
      // 清除现有标记
      Object.values(this.waypointMarkers).forEach(marker => {
        marker.setMap(null);
      });
      this.waypointMarkers = {};

      // 重新创建所有航点标记
      this.currentAirline.waypoints.forEach(waypoint => {
        this.createWaypointMarker(waypoint);
      });

      // 重新创建起飞点标记
      if (this.takeoffPoint) {
        this.createTakeoffPointMarker();
      }

      // 更新连线
      this.updateWaypointLines();

      console.log('航点标记重新创建完成');
    },

    // 初始化3D航点管理器
    initThreeWaypointManager() {
      try {
        if (this.$refs.amap && this.$refs.amap.map) {
          const map = this.$refs.amap.map

          // 检查customCoords是否可用
          if (!map.customCoords) {
            console.warn('customCoords未准备好，延迟初始化3D航点管理器')
            setTimeout(() => {
              this.initThreeWaypointManager()
            }, 1000)
            return
          }

          this.threeWaypointManager = new ThreeWaypointManager(map)
          console.log('3D航点管理器初始化成功')
        } else {
          console.warn('地图未准备好，延迟初始化3D航点管理器')
          setTimeout(() => {
            this.initThreeWaypointManager()
          }, 1000)
        }
      } catch (error) {
        console.error('3D航点管理器初始化失败:', error)
        this.use3DWaypoints = false // 降级到2D模式
        this.$message.error('3D航点初始化失败，已切换到2D模式')
      }
    },

    // 初始化3D航线管理器
    initThreeFlightPathManager() {
      try {
        if (this.$refs.amap && this.$refs.amap.map) {
          const map = this.$refs.amap.map

          // 检查customCoords是否可用
          if (!map.customCoords) {
            console.warn('customCoords未准备好，延迟初始化3D航线管理器')
            setTimeout(() => {
              this.initThreeFlightPathManager()
            }, 1000)
            return
          }

          this.threeFlightPathManager = new ThreeFlightPathManager(map)
          console.log('3D航线管理器初始化成功')

          // 如果已有航点，创建3D航线
          if (this.currentAirline.waypoints.length >= 2) {
            this.create3DFlightPath()
          }
        } else {
          console.warn('地图未准备好，延迟初始化3D航线管理器')
          setTimeout(() => {
            this.initThreeFlightPathManager()
          }, 1000)
        }
      } catch (error) {
        console.error('3D航线管理器初始化失败:', error)
        this.use3DFlightPath = false // 降级到2D模式
        this.$message.error('3D航线初始化失败，已切换到2D模式')
      }
    },

    // 切换3D航线功能
    toggle3DFlightPath() {
      this.use3DFlightPath = !this.use3DFlightPath

      if (this.use3DFlightPath) {
        // 启用3D航线
        if (!this.is3DMode) {
          this.$message.warning('建议先切换到3D视角以获得更好的效果')
        }

        setTimeout(() => {
          this.initThreeFlightPathManager()
        }, 1000)

        this.$message.success('3D航线已启用')
      } else {
        // 禁用3D航线
        if (this.threeFlightPathManager) {
          this.threeFlightPathManager.dispose()
          this.threeFlightPathManager = null
        }
        this.$message.success('3D航线已禁用')
      }
    },

    // 创建3D航线路径
    create3DFlightPath() {
      if (!this.use3DFlightPath || !this.threeFlightPathManager) {
        return
      }

      if (this.currentAirline.waypoints.length < 2) {
        console.warn('航线至少需要2个航点')
        return
      }

      try {
        // 删除现有的3D航线
        this.threeFlightPathManager.removeFlightPath('current_airline')

        // 创建新的3D航线
        const result = this.threeFlightPathManager.createFlightPath(
          'current_airline',
          this.currentAirline.waypoints,
          {
            name: this.currentAirline.name || '当前航线',
            color: 0x00ff00
          }
        )

        if (result) {
          console.log('3D航线创建成功:', result)

          // 创建飞行器
          this.threeFlightPathManager.createAircraft(
            'aircraft_1',
            'current_airline',
            {
              speed: this.currentAirline.defaultSpeed * 10 || 50 // 转换为合适的显示速度
            }
          )
        }
      } catch (error) {
        console.error('3D航线创建失败:', error)
        this.$message.error('3D航线创建失败: ' + error.message)
      }
    },

    // 检测初始3D状态
    detectInitial3DState() {
      if (!this.$refs.amap || !this.$refs.amap.map) return;

      const map = this.$refs.amap.map;

      try {
        // 检查当前俯仰角度
        const currentPitch = map.getPitch ? map.getPitch() : 0;

        // 如果俯仰角大于5度，认为是3D视角
        if (currentPitch > 5) {
          this.is3DMode = true;
          // 加载3D控制插件
          this.load3DControls();
        } else {
          this.is3DMode = false;
        }

        console.log('初始视角检测完成，当前俯仰角:', currentPitch, '3D模式:', this.is3DMode);
      } catch (error) {
        console.warn('检测初始3D状态失败:', error);
        // 默认为2D视角
        this.is3DMode = false;
      }
    },

    // 创建3D航点标记
    create3DWaypointMarker(waypoint, index) {
      const markerContent = document.createElement('div')
      markerContent.className = 'waypoint-marker-3d'

      // 计算高度相关参数
      const height = waypoint.height || this.currentAirline.defaultHeight || 100
      const heightLineLength = Math.max(30, Math.min(height * 1.2, 200)) // 高度线长度，最小30px，最大200px
      const shadowSize = Math.max(8, Math.min(height * 0.15, 25)) // 阴影大小
      const perspective = height * 2 // 透视距离

      // 创建增强的3D航点标记结构
      markerContent.innerHTML = `
        <div class="waypoint-3d-scene" style="perspective: ${perspective}px;">
          <div class="waypoint-3d-container">
            <!-- 高度标签 -->
            <div class="height-label">${height}m</div>

            <!-- 3D航点球体 -->
            <div class="waypoint-sphere-container">
              <div class="waypoint-sphere">
                <div class="waypoint-sphere-inner">
                  <span class="waypoint-number">${index}</span>
                </div>
                <!-- 球体高光 -->
                <div class="sphere-highlight"></div>
              </div>
            </div>

            <!-- 3D垂直支柱 -->
            <div class="height-pillar" style="height: ${heightLineLength}px;">
              <div class="pillar-face pillar-front"></div>
              <div class="pillar-face pillar-back"></div>
              <div class="pillar-face pillar-left"></div>
              <div class="pillar-face pillar-right"></div>
              <div class="pillar-top"></div>
            </div>

            <!-- 地面阴影 -->
            <div class="ground-shadow" style="width: ${shadowSize}px; height: ${shadowSize * 0.6}px;"></div>

            <!-- 地面基座 -->
            <div class="ground-base">
              <div class="base-ring"></div>
              <div class="base-center"></div>
            </div>
          </div>
        </div>
      `

      // 设置基础样式
      this.apply3DMarkerStyles(markerContent, waypoint, height)

      return markerContent
    },

    // 应用3D标记样式
    apply3DMarkerStyles(markerContent, waypoint, height) {
      markerContent.style.cssText = `
        position: relative;
        cursor: pointer;
        width: 50px;
        height: auto;
        transform-style: preserve-3d;
        transition: all 0.3s ease;
      `

      // 3D场景容器
      const scene = markerContent.querySelector('.waypoint-3d-scene')
      if (scene) {
        scene.style.cssText = `
          position: relative;
          width: 100%;
          height: 100%;
          transform-style: preserve-3d;
        `
      }

      // 3D容器样式
      const container = markerContent.querySelector('.waypoint-3d-container')
      if (container) {
        container.style.cssText = `
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          transform-style: preserve-3d;
          transform: rotateX(15deg) rotateY(5deg);
          transition: transform 0.3s ease;
        `
      }

      // 高度标签样式
      const heightLabel = markerContent.querySelector('.height-label')
      if (heightLabel) {
        heightLabel.style.cssText = `
          position: absolute;
          top: -30px;
          left: 50%;
          transform: translateX(-50%) rotateX(-15deg);
          background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
          color: #ffffff;
          padding: 4px 10px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: bold;
          white-space: nowrap;
          pointer-events: none;
          z-index: 1000;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
          border: 1px solid rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(4px);
        `
      }

      // 3D球体容器
      const sphereContainer = markerContent.querySelector('.waypoint-sphere-container')
      if (sphereContainer) {
        sphereContainer.style.cssText = `
          position: relative;
          width: 40px;
          height: 40px;
          margin-bottom: 4px;
          z-index: 100;
          transform-style: preserve-3d;
        `
      }

      // 3D球体
      const sphere = markerContent.querySelector('.waypoint-sphere')
      if (sphere) {
        sphere.style.cssText = `
          position: relative;
          width: 40px;
          height: 40px;
          transform-style: preserve-3d;
          animation: sphereFloat 3s ease-in-out infinite;
        `
      }

      // 球体内部
      const sphereInner = markerContent.querySelector('.waypoint-sphere-inner')
      if (sphereInner) {
        sphereInner.style.cssText = `
          width: 40px;
          height: 40px;
          background: radial-gradient(circle at 25% 25%, #7cb342, #52c41a, #389e0d);
          border: 3px solid #ffffff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow:
            0 8px 20px rgba(56, 158, 13, 0.5),
            inset 0 4px 8px rgba(255, 255, 255, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2);
          position: relative;
          transition: all 0.3s ease;
          transform: translateZ(10px);
        `
      }

      // 球体高光
      const highlight = markerContent.querySelector('.sphere-highlight')
      if (highlight) {
        highlight.style.cssText = `
          position: absolute;
          top: 6px;
          left: 8px;
          width: 12px;
          height: 8px;
          background: radial-gradient(ellipse, rgba(255, 255, 255, 0.8), transparent);
          border-radius: 50%;
          pointer-events: none;
          transform: translateZ(15px);
        `
      }

      // 3D支柱
      const pillar = markerContent.querySelector('.height-pillar')
      if (pillar) {
        pillar.style.cssText = `
          position: relative;
          width: 6px;
          margin: 0 auto;
          z-index: 50;
          transform-style: preserve-3d;
          transform: translateZ(-3px);
        `
      }

      // 支柱各个面
      const pillarFaces = markerContent.querySelectorAll('.pillar-face')
      pillarFaces.forEach((face, index) => {
        const transforms = [
          'rotateY(0deg) translateZ(3px)',    // front
          'rotateY(180deg) translateZ(3px)',  // back
          'rotateY(-90deg) translateZ(3px)',  // left
          'rotateY(90deg) translateZ(3px)'    // right
        ]

        face.style.cssText = `
          position: absolute;
          width: 6px;
          height: 100%;
          background: linear-gradient(to bottom,
            rgba(82, 196, 26, 0.9) 0%,
            rgba(82, 196, 26, 0.7) 50%,
            rgba(82, 196, 26, 0.5) 100%
          );
          transform: ${transforms[index]};
          box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.3);
        `
      })

      // 支柱顶部
      const pillarTop = markerContent.querySelector('.pillar-top')
      if (pillarTop) {
        pillarTop.style.cssText = `
          position: absolute;
          top: 0;
          left: 0;
          width: 6px;
          height: 6px;
          background: radial-gradient(circle, #7cb342, #52c41a);
          transform: rotateX(90deg) translateZ(3px);
          border-radius: 50%;
        `
      }

      // 地面阴影
      const groundShadow = markerContent.querySelector('.ground-shadow')
      if (groundShadow) {
        groundShadow.style.cssText = `
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
          background: radial-gradient(ellipse, rgba(0, 0, 0, 0.3), transparent);
          border-radius: 50%;
          z-index: 1;
        `
      }

      // 地面基座
      const groundBase = markerContent.querySelector('.ground-base')
      if (groundBase) {
        groundBase.style.cssText = `
          position: relative;
          width: 16px;
          height: 16px;
          margin: 2px auto 0;
          z-index: 10;
        `
      }

      // 基座环
      const baseRing = markerContent.querySelector('.base-ring')
      if (baseRing) {
        baseRing.style.cssText = `
          position: absolute;
          width: 16px;
          height: 16px;
          border: 2px solid rgba(82, 196, 26, 0.6);
          border-radius: 50%;
          background: rgba(82, 196, 26, 0.1);
          box-shadow: 0 0 8px rgba(82, 196, 26, 0.3);
        `
      }

      // 基座中心
      const baseCenter = markerContent.querySelector('.base-center')
      if (baseCenter) {
        baseCenter.style.cssText = `
          position: absolute;
          top: 50%;
          left: 50%;
          width: 6px;
          height: 6px;
          transform: translate(-50%, -50%);
          background: radial-gradient(circle, #52c41a, #389e0d);
          border-radius: 50%;
          box-shadow: 0 0 4px rgba(82, 196, 26, 0.5);
        `
      }

      // 数字样式
      const numberSpan = markerContent.querySelector('.waypoint-number')
      if (numberSpan) {
        numberSpan.style.cssText = `
          color: #ffffff;
          font-size: 16px;
          font-weight: bold;
          line-height: 1;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
          pointer-events: none;
          transform: translateZ(20px);
        `
      }

      // 添加增强的3D悬停效果
      markerContent.addEventListener('mouseenter', () => {
        const container = markerContent.querySelector('.waypoint-3d-container')
        const sphereInner = markerContent.querySelector('.waypoint-sphere-inner')

        if (container) {
          container.style.transform = 'rotateX(20deg) rotateY(10deg) scale(1.1)'
        }

        if (sphereInner) {
          sphereInner.style.transform = 'translateZ(15px) scale(1.1)'
          sphereInner.style.boxShadow = `
            0 12px 30px rgba(56, 158, 13, 0.6),
            inset 0 6px 12px rgba(255, 255, 255, 0.4),
            inset 0 -3px 6px rgba(0, 0, 0, 0.3)
          `
        }
      })

      markerContent.addEventListener('mouseleave', () => {
        const container = markerContent.querySelector('.waypoint-3d-container')
        const sphereInner = markerContent.querySelector('.waypoint-sphere-inner')

        if (container) {
          container.style.transform = 'rotateX(15deg) rotateY(5deg) scale(1)'
        }

        if (sphereInner) {
          sphereInner.style.transform = 'translateZ(10px) scale(1)'
          sphereInner.style.boxShadow = `
            0 8px 20px rgba(56, 158, 13, 0.5),
            inset 0 4px 8px rgba(255, 255, 255, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2)
          `
        }
      })
    },

    // 添加增强的拖拽事件（支持Ctrl+拖拽改变高度）
    addEnhancedDragEvents(marker, waypoint) {
      let isDragging = false
      let isHeightDragging = false
      let startY = 0
      let startHeight = waypoint.height

      // 拖拽开始事件
      marker.on('dragstart', (e) => {
        console.log('开始拖拽航点:', waypoint.id, 'Ctrl键状态:', this.isCtrlPressed)
        this.selectWaypoint(waypoint.id) // 拖拽时自动选中
        isDragging = true

        // 检查是否按住Ctrl键 - 使用全局状态而不是事件对象
        if (this.isCtrlPressed || (e.originEvent && e.originEvent.ctrlKey)) {
          isHeightDragging = true
          startY = e.originEvent ? e.originEvent.clientY : 0
          startHeight = waypoint.height || this.currentAirline.defaultHeight

          // 禁用地图拖拽，启用高度调整模式
          marker.setDraggable(false)
          this.$message.info('高度调整模式：上下拖动改变航点高度')

          // 添加临时的鼠标移动监听
          this.addHeightDragListeners(marker, waypoint, startY, startHeight)
        }
      })

      // 拖拽结束事件
      marker.on('dragend', (e) => {
        console.log('拖拽结束:', e.lnglat)
        isDragging = false

        if (!isHeightDragging) {
          // 普通位置拖拽
          this.updateWaypointPosition(waypoint.id, e.lnglat.lng, e.lnglat.lat)
        }

        isHeightDragging = false
      })

      // 监听全局Ctrl键状态变化，在拖拽过程中切换模式
      const checkCtrlState = () => {
        if (isDragging && !isHeightDragging && this.isCtrlPressed) {
          // 在普通拖拽过程中按下Ctrl键，切换到高度调整模式
          isHeightDragging = true
          startY = 0 // 重置起始Y坐标
          startHeight = waypoint.height || this.currentAirline.defaultHeight
          marker.setDraggable(false)
          
          this.$message.info('已切换到高度调整模式')

          // 启动高度拖拽监听
          this.addHeightDragListeners(marker, waypoint, startY, startHeight)
        } else if (isDragging && isHeightDragging && !this.isCtrlPressed) {
          // 在高度拖拽过程中释放Ctrl键，恢复位置拖拽模式
          isHeightDragging = false
          marker.setDraggable(true)
          
          this.$message.info('已切换到位置拖拽模式')
        }
      }

      // 使用定时器检查Ctrl键状态变化（避免重复添加事件监听器）
      let ctrlCheckInterval = null

      // 添加额外的事件监听器来处理拖拽过程中的Ctrl键状态变化
      marker.on('dragstart', () => {
        ctrlCheckInterval = setInterval(checkCtrlState, 100) // 每100ms检查一次
      })

      marker.on('dragend', () => {
        if (ctrlCheckInterval) {
          clearInterval(ctrlCheckInterval)
          ctrlCheckInterval = null
        }
      })
    },

    // 添加高度拖拽监听器
    addHeightDragListeners(marker, waypoint, startY, startHeight) {
      const handleMouseMove = (e) => {
        if (!this.isHeightDragging) return

        const deltaY = startY - e.clientY // 向上为正
        const heightChange = deltaY * 0.5 // 调整灵敏度
        const newHeight = Math.max(10, Math.min(500, startHeight + heightChange))

        // 更新航点高度
        const waypointIndex = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
        if (waypointIndex !== -1) {
          this.currentAirline.waypoints[waypointIndex].height = Math.round(newHeight)

          // 实时更新3D标记显示
          this.update3DMarkerHeight(waypoint.id, newHeight)

          // 如果启用了3D航点且管理器已初始化，同步更新3D航点高度
          if (this.use3DWaypoints && this.threeWaypointManager) {
            try {
              this.threeWaypointManager.updateWaypointHeight(waypoint.id, Math.round(newHeight))
            } catch (error) {
              console.error('3D航点高度拖拽更新失败:', error)
            }
          }

          // 强制更新Vue组件以同步右侧列表显示
          this.$forceUpdate()
        }
      }

      const handleMouseUp = () => {
        this.isHeightDragging = false
        marker.setDraggable(true)

        // 移除临时监听器
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)

        // 标记数据已变更
        this.markDataChanged()

        const waypointIndex = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
        this.$message.success(`航点 ${waypointIndex + 1} 高度已更新为 ${waypoint.height}m`)
      }

      // 添加临时监听器
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      this.isHeightDragging = true
    },

    // 更新3D标记高度显示
    update3DMarkerHeight(waypointId, newHeight) {
      const marker = this.waypointMarkers[waypointId]
      if (!marker) return

      const markerContent = marker.getContent()
      if (!markerContent) return

      // 更新高度标签
      const heightLabel = markerContent.querySelector('.height-label')
      if (heightLabel) {
        heightLabel.textContent = `${Math.round(newHeight)}m`
      }

      // 更新3D支柱高度
      const pillar = markerContent.querySelector('.height-pillar')
      if (pillar) {
        const heightLineLength = Math.max(30, Math.min(newHeight * 1.2, 200))
        pillar.style.height = `${heightLineLength}px`
      }

      // 更新地面阴影大小
      const groundShadow = markerContent.querySelector('.ground-shadow')
      if (groundShadow) {
        const shadowSize = Math.max(8, Math.min(newHeight * 0.15, 25))
        groundShadow.style.width = `${shadowSize}px`
        groundShadow.style.height = `${shadowSize * 0.6}px`
      }

      // 更新3D场景透视
      const scene = markerContent.querySelector('.waypoint-3d-scene')
      if (scene) {
        const perspective = newHeight * 2
        scene.style.perspective = `${perspective}px`
      }

      // 添加高度变化的视觉反馈
      const sphereInner = markerContent.querySelector('.waypoint-sphere-inner')
      if (sphereInner) {
        // 临时高亮效果
        sphereInner.style.background = 'radial-gradient(circle at 25% 25%, #ff9c6e, #ff7875, #f5222d)'
        sphereInner.style.boxShadow = `
          0 12px 30px rgba(245, 34, 45, 0.6),
          inset 0 6px 12px rgba(255, 255, 255, 0.4),
          inset 0 -3px 6px rgba(0, 0, 0, 0.3)
        `

        // 500ms后恢复正常颜色
        setTimeout(() => {
          sphereInner.style.background = 'radial-gradient(circle at 25% 25%, #7cb342, #52c41a, #389e0d)'
          sphereInner.style.boxShadow = `
            0 8px 20px rgba(56, 158, 13, 0.5),
            inset 0 4px 8px rgba(255, 255, 255, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2)
          `
        }, 500)
      }
    },

   

   

    // 加载3D视角控制插件
    load3DControls() {
      if (!this.$refs.amap || !this.$refs.amap.map) return;

      // 使用异步方式加载插件，避免阻塞
      setTimeout(() => {
        AMap.plugin(['AMap.ControlBar', 'AMap.ToolBar'], () => {
          const map = this.$refs.amap.map;
          // 添加缩放控制插件（右下角）
          if (!this.toolBar) {
            this.toolBar = new AMap.ToolBar({
              position: {
                right: '10px',
                bottom: '80px'
              },
              locate: false, // 不显示定位按钮
              noIpLocate: true, // 不使用IP定位
              liteStyle: true // 使用简洁样式
            });
            map.addControl(this.toolBar);
          }

          console.log('3D视角控制插件加载完成');
        });
      }, 100);
    },


    // 切换卫星图层
    async toggleSatelliteLayer() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }

      const map = this.$refs.amap.map;

      if (this.showSatelliteLayer) {
        // 关闭卫星图层
        if (this.satelliteLayer) {
          map.remove(this.satelliteLayer);
          this.satelliteLayer = null;
        }
        this.showSatelliteLayer = false;
        this.$message.info('已切换到标准地图');
      } else {
        try {
          // 直接使用卫星图层方式
          await new Promise((resolve) => {
            AMap.plugin(['AMap.TileLayer.Satellite'], resolve);
          });

          this.satelliteLayer = new AMap.TileLayer.Satellite({
            zIndex: 1,
            opacity: 1,
            detectRetina: true,
            reuseTile: true
          });

          map.add(this.satelliteLayer);
          this.showSatelliteLayer = true;
          this.$message.success('已切换到卫星图');

        } catch (error) {
          console.error('卫星图层加载失败:', error);
          this.$message.error('卫星图加载失败，请稍后重试');
        }
      }
    },

    // 切换路况图层
    async toggleTrafficLayer() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.warning('地图未初始化');
        return;
      }

      const map = this.$refs.amap.map;

      if (this.showTrafficLayer) {
        // 关闭路况图层
        if (this.trafficLayer) {
          map.remove(this.trafficLayer);
          this.trafficLayer = null;
        }
        this.showTrafficLayer = false;
        this.$message.info('路况信息已关闭');
      } else {
        try {
          await new Promise((resolve) => {
            AMap.plugin(['AMap.TileLayer.Traffic'], resolve);
          });

          this.trafficLayer = new AMap.TileLayer.Traffic({
            zIndex: 10,
            opacity: 0.8,
            autoRefresh: true,
            interval: 180
          });

          map.add(this.trafficLayer);
          this.showTrafficLayer = true;
          this.$message.success('路况信息已开启');

        } catch (error) {
          console.error('路况图层加载失败:', error);
          this.$message.error('路况信息加载失败');
        }
      }
    },

    // 切换3D航点模式
    async toggle3DWaypoints() {
      try {
        this.use3DWaypoints = !this.use3DWaypoints

        if (this.use3DWaypoints) {
          // 切换到3D航点模式
          if (!this.threeWaypointManager) {
            this.initThreeWaypointManager()
          }
          this.$message.success('已切换到3D航点模式')
        } else {
          // 切换到2D航点模式
          if (this.threeWaypointManager) {
            this.threeWaypointManager.dispose()
            this.threeWaypointManager = null
          }
          this.$message.success('已切换到2D航点模式')
        }

        // 重新更新航点显示
        this.updateMapWaypoints()

      } catch (error) {
        console.error('切换航点模式失败:', error)
        this.$message.error('切换航点模式失败')
        // 回滚状态
        this.use3DWaypoints = !this.use3DWaypoints
      }
    },

    // 测试3D航点功能
    test3DWaypoint() {
      console.log('开始测试3D航点功能')

      if (!this.use3DWaypoints) {
        this.$message.info('请先启用3D航点模式')
        return
      }

      if (!this.threeWaypointManager) {
        this.$message.error('3D航点管理器未初始化')
        return
      }

      try {
        // 清除现有航点
        this.clearMapMarkers()

        // 添加测试航点
        const testWaypoints = [
          { id: 'test1', lng: 116.52, lat: 39.79, height: 100 },
          { id: 'test2', lng: 116.54, lat: 39.79, height: 150 },
          { id: 'test3', lng: 116.56, lat: 39.79, height: 200 }
        ]

        testWaypoints.forEach((wp, index) => {
          const result = this.threeWaypointManager.addWaypoint(
            wp.id,
            wp.lng,
            wp.lat,
            wp.height,
            { index: index + 1 }
          )
          console.log(`添加测试航点 ${wp.id}:`, result)
        })

        this.$message.success('测试3D航点已添加')

        // 将地图中心移动到第一个测试航点
        this.$refs.amap.map.setCenter([116.54, 39.79])
        this.$refs.amap.map.setZoom(15)

      } catch (error) {
        console.error('测试3D航点失败:', error)
        this.$message.error('测试3D航点失败: ' + error.message)
      }
    },

    // 批量编辑航点
    batchEditWaypoints() {
      if (!this.currentAirline || this.currentAirline.waypoints.length === 0) {
        this.$message.warning('没有航点可编辑');
        return;
      }

      // 重置表单
      this.batchEditOptions = [];
      this.batchEditForm = {
        height: this.currentAirline.defaultHeight || 100,
        speed: this.currentAirline.defaultSpeed || 5,
        applyTo: 'all',
        startIndex: 1,
        endIndex: this.currentAirline.waypoints.length
      };

      this.batchEditDialogVisible = true;
    },

    // 应用批量编辑
    applyBatchEdit() {
      if (this.batchEditOptions.length === 0) {
        this.$message.warning('请选择要批量设置的属性');
        return;
      }

      let targetWaypoints = [];

      // 确定要编辑的航点范围
      switch (this.batchEditForm.applyTo) {
        case 'all':
          targetWaypoints = this.currentAirline.waypoints;
          break;
        case 'selected':
          if (this.selectedWaypointId) {
            targetWaypoints = this.currentAirline.waypoints.filter(w => w.id === this.selectedWaypointId);
          } else {
            this.$message.warning('请先选择一个航点');
            return;
          }
          break;
        case 'range':
          const startIdx = Math.max(0, this.batchEditForm.startIndex - 1);
          const endIdx = Math.min(this.currentAirline.waypoints.length, this.batchEditForm.endIndex);
          targetWaypoints = this.currentAirline.waypoints.slice(startIdx, endIdx);
          break;
      }

      if (targetWaypoints.length === 0) {
        this.$message.warning('没有找到要编辑的航点');
        return;
      }

      // 应用批量编辑
      let changedCount = 0;
      targetWaypoints.forEach(waypoint => {
        if (this.batchEditOptions.includes('height')) {
          waypoint.height = this.batchEditForm.height;
          changedCount++;
        }
        if (this.batchEditOptions.includes('speed')) {
          waypoint.speed = this.batchEditForm.speed;
          changedCount++;
        }
      });

      this.batchEditDialogVisible = false;
      this.markDataChanged();
      this.$message.success(`已批量编辑 ${targetWaypoints.length} 个航点`);
    },

    // 批量编辑对话框关闭事件
    onBatchEditDialogClose() {
      this.batchEditOptions = [];
    },

    // 为选中的航点添加动作
    addActionToSelectedWaypoint(actionType) {
      if (!this.selectedWaypointId) {
        this.$message.warning('请先选择一个航点')
        return
      }

      const waypoint = this.currentAirline.waypoints.find(w => w.id === this.selectedWaypointId)
      if (!waypoint) {
        this.$message.error('未找到选中的航点')
        return
      }

      // 创建新动作，使用序号作为ID
      const action = {
        id: waypoint.actions.length + 1, // 使用序号作为ID
        type: actionType,
        params: this.getDefaultActionParams(actionType),
        createTime: new Date()
      }

      // 检查是否需要参数设置
      if (this.actionNeedsParams(actionType)) {
        // 需要参数设置，打开对话框
        this.openActionParamsDialog(action, waypoint)
      } else {
        // 不需要参数设置，直接添加到航点
        waypoint.actions.push(action)
        // 标记数据已变更
        this.markDataChanged()
        this.$message.success(`已添加${this.getActionName(actionType)}动作`)
      }
    },

    // 打开动作参数设置对话框
    openActionParamsDialog(action, waypoint) {
      this.currentEditingAction = JSON.parse(JSON.stringify(action)) // 深拷贝
      this.currentEditingWaypointIndex = this.currentAirline.waypoints.findIndex(w => w.id === waypoint.id)
      this.actionParamsDialogVisible = true
    },

    // 编辑现有动作参数
    editActionParams(waypointId, actionId) {
      const waypoint = this.currentAirline.waypoints.find(w => w.id === waypointId)
      if (!waypoint) return

      const action = waypoint.actions.find(a => a.id === actionId)
      if (!action) return

      this.openActionParamsDialog(action, waypoint)
    },

    // 显示动作右键菜单
    showActionContextMenu(event, waypoint, action) {
      this.contextMenuVisible = true
      this.contextMenuPosition = {
        x: event.clientX,
        y: event.clientY
      }
      this.contextMenuWaypoint = waypoint
      this.contextMenuAction = action

      // 阻止默认右键菜单
      event.preventDefault()
      event.stopPropagation()
    },

    // 隐藏右键菜单
    hideContextMenu() {
      this.contextMenuVisible = false
      this.contextMenuWaypoint = null
      this.contextMenuAction = null
    },

    // 从右键菜单删除动作
    deleteActionFromContextMenu() {
      if (!this.contextMenuWaypoint || !this.contextMenuAction) return

      const actionIndex = this.contextMenuWaypoint.actions.findIndex(
        action => action.id === this.contextMenuAction.id ||
        (action.type === this.contextMenuAction.type && action.createTime === this.contextMenuAction.createTime)
      )

      if (actionIndex !== -1) {
        this.contextMenuWaypoint.actions.splice(actionIndex, 1)
        this.reassignActionIds(this.contextMenuWaypoint)

        const actionName = this.getActionName(this.contextMenuAction.type)
        this.$message.success(`已删除${actionName}动作`)
      }

      this.hideContextMenu()
    },

    // 获取默认动作参数
    getDefaultActionParams(actionType) {
      const defaultParams = {
        'photo': {},
        'video_start': {},
        'video_stop': {},
        'panorama': {},
        'gimbal_yaw': {
          pitch: -45,
          yaw: 0,
          rotateTime: 0
        },
        'drone_yaw': {
          angle: 0,
          mode: 'shortest'
        },
        'hover': {
          duration: 3
        },
        'zoom': {
          factor: 2.0,
          zoomTime: 1.0
        }
      }
      return JSON.parse(JSON.stringify(defaultParams[actionType] || {}))
    },

    // 检查动作是否需要参数设置
    actionNeedsParams(actionType) {
      const noParamsActions = ['photo', 'video_start', 'video_stop', 'panorama']
      return !noParamsActions.includes(actionType)
    },

    // 获取动作的提示信息（包含参数）
    getActionTooltip(action) {
      const actionName = this.getActionName(action.type)

      // 无参数动作直接返回动作名称
      if (!this.actionNeedsParams(action.type)) {
        return actionName
      }

      // 有参数动作显示参数信息
      const params = action.params || {}
      let tooltip = `${actionName}\n────────────`

      switch (action.type) {
        case 'gimbal_yaw':
          tooltip += `\n• 俯仰角: ${params.pitch !== undefined ? params.pitch : -45}°`
          tooltip += `\n• 偏航角: ${params.yaw !== undefined ? params.yaw : 0}°`
          tooltip += `\n• 转动时间: ${params.rotateTime !== undefined ? params.rotateTime : 0}秒`
          break

        case 'zoom':
          tooltip += `\n• 变焦倍数: ${params.factor !== undefined ? params.factor : 2.0}倍`
          tooltip += `\n• 变焦时间: ${params.zoomTime !== undefined ? params.zoomTime : 1.0}秒`
          break

        case 'hover':
          tooltip += `\n• 悬停时间: ${params.duration !== undefined ? params.duration : 3}秒`
          break

        case 'drone_yaw':
          const modeText = {
            'clockwise': '顺时针',
            'counterclockwise': '逆时针',
            'shortest': '最短路径'
          }
          tooltip += `\n• 偏航角: ${params.angle !== undefined ? params.angle : 0}°`
          tooltip += `\n• 转动模式: ${modeText[params.mode] || '最短路径'}`
          break

        default:
          // 其他动作类型的参数显示
          if (Object.keys(params).length > 0) {
            Object.entries(params).forEach(([key, value]) => {
              tooltip += `\n• ${key}: ${value}`
            })
          } else {
            tooltip += `\n• 无需设置参数`
          }
          break
      }

      return tooltip
    },

    // 获取动作名称
    getActionName(actionType) {
      const actionNames = {
        'takePhoto': '单拍',
        'startRecord': '开始录像',
        'stopRecord': '结束录像',
        'focus': '对焦',
        'zoom': '变焦',
        'customDirName': '创建新文件夹',
        'gimbalRotate': '旋转云台',
        'rotateYaw': '飞行器偏航',
        'hover': '悬停等待',
        'gimbalEvenlyRotate': '航段间均匀转动云台pitch角',
        'orientedShoot': '定向拍照动作',
        'panoShot': '全景拍照动作',
        'recordPointCloud': '点云录制操作'
      }
      return actionNames[actionType] || actionType
    },

    // 获取动作简短名称（用于文字显示）
    getActionShortName(actionType) {
      const shortNames = {
        'takePhoto': '单拍',
        'startRecord': '录像',
        'stopRecord': '停止',
        'focus': '对焦',
        'zoom': '变焦',
        'customDirName': '文件夹',
        'gimbalRotate': '云台',
        'rotateYaw': '偏航',
        'hover': '悬停',
        'gimbalEvenlyRotate': '均匀转动',
        'orientedShoot': '定向拍照',
        'panoShot': '全景',
        'recordPointCloud': '点云'
      }
      return shortNames[actionType] || actionType.substring(0, 2).toUpperCase()
    },

    // 更新航点信息
    updateWaypoint(waypoint) {
      // 确保数值类型正确
      if (waypoint.speed !== '' && waypoint.speed !== null) {
        waypoint.speed = Number(waypoint.speed)
      }
      if (waypoint.height !== '' && waypoint.height !== null) {
        waypoint.height = Number(waypoint.height)

        // 如果启用了3D航点且管理器已初始化，同步更新3D航点高度
        if (this.use3DWaypoints && this.threeWaypointManager) {
          try {
            this.threeWaypointManager.updateWaypointHeight(waypoint.id, waypoint.height)
            console.log('3D航点高度更新成功:', waypoint.id, waypoint.height)
          } catch (error) {
            console.error('3D航点高度更新失败:', error)
          }
        }
      }

      // 标记数据已变更
      this.markDataChanged()

      console.log('航点更新:', waypoint)
    },

    // 获取动作对应的图标
    getActionIcon(actionType) {
      const iconMap = {
        'takePhoto': 'wrj_dz_zxj',
        'startRecord': 'wrj_dz_kslx',
        'stopRecord': 'wrj_dz_jslx',
        'focus': 'wrj_dz_jj',
        'zoom': 'wrj_dz_bj',
        'customDirName': 'wrj_dz_wjj',
        'gimbalRotate': 'wrj_dz_wrjytph',
        'rotateYaw': 'wrj_dz_wrjph',
        'hover': 'wrj_dz_wrjxt',
        'gimbalEvenlyRotate': 'wrj_dz_wrjytph',
        'orientedShoot': 'wrj_dz_zxj',
        'panoShot': 'wrj_dz_qjpz',
        'recordPointCloud': 'wrj_dz_dyl'
      }
      return iconMap[actionType] || 'default'
    },

    // 工具方法
    generateId() {
      return 'wp_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    // 计算两点间距离
    calculateDistance(point1, point2) {
      const R = 6371000 // 地球半径（米）
      const lat1 = point1.latitude * Math.PI / 180
      const lat2 = point2.latitude * Math.PI / 180
      const deltaLat = (point2.latitude - point1.latitude) * Math.PI / 180
      const deltaLng = (point2.longitude - point1.longitude) * Math.PI / 180

      const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                Math.sin(deltaLng / 2) * Math.sin(deltaLng / 2)
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

      return R * c
    },

    // 计算总距离
    calculateTotalDistance(waypoints) {
      if (waypoints.length < 2) return 0

      let totalDistance = 0
      for (let i = 0; i < waypoints.length - 1; i++) {
        totalDistance += this.calculateDistance(waypoints[i], waypoints[i + 1])
      }

      return Math.round(totalDistance)
    },

    // 计算预计时间
    calculateEstimatedTime(waypoints) {
      if (waypoints.length < 2) return 0

      const totalDistance = this.calculateTotalDistance(waypoints)
      const averageSpeed = this.currentAirline ? this.currentAirline.defaultSpeed : 5
      const timeInSeconds = totalDistance / averageSpeed

      return Math.round(timeInSeconds / 60) // 转换为分钟
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()
    },

    // 获取动作名称（重复定义，删除此函数）
    // getActionName(actionType) {
    //   return this.actionTypes[actionType]?.name || actionType
    // },

    // 获取动作标签类型
    getActionTagType(actionType) {
      return this.actionTypes[actionType]?.tag || ''
    },

    // 导出航线
    exportAirline() {
      if (!this.currentAirline) {
        this.$message.warning('请先选择一个航线')
        return
      }

      const exportData = {
        version: '1.0',
        airline: this.currentAirline,
        exportTime: new Date().toISOString(),
        format: 'flight-ui-wpml'
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      })

      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${this.currentAirline.name}_${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      this.$message.success('航线导出成功')
    },

    // 导入航线
    importAirline() {
      this.$refs.fileInput.click()
    },

    // 处理文件导入
    handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)

          if (data.format === 'flight-ui-wpml' && data.airline) {
            const importedAirline = {
              ...data.airline,
              id: this.generateId(),
              name: `${data.airline.name}_导入`,
              createTime: new Date(),
              updateTime: new Date(),
              waypoints: data.airline.waypoints.map(waypoint => ({
                ...waypoint,
                id: this.generateId()
              }))
            }

            this.airlineList.push(importedAirline)
            this.saveAirlines()
            this.selectAirline(importedAirline.id)

            this.$message.success('航线导入成功')
          } else {
            this.$message.error('不支持的文件格式')
          }
        } catch (error) {
          console.error('导入失败:', error)
          this.$message.error('文件解析失败')
        }
      }

      reader.readAsText(file)

      // 清空文件输入
      event.target.value = ''
    },


  }
}
</script>

<style scoped>
.airline-container {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.map-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 悬浮控制按钮 */
.floating-controls {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 1000;
  align-items: flex-end;
}

.floating-btn {
  width: 48px;
  height: 48px;
  background: rgba(44, 44, 44, 0.9);
  border: 1px solid #404040;
  border-radius: 8px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.floating-btn:hover {
  background: rgba(24, 144, 255, 0.9);
  border-color: #1890ff;
  color: #ffffff;
  transform: scale(1.1);
}

.floating-btn.recording {
  background: rgba(255, 77, 79, 0.9);
  border-color: #ff4d4f;
  color: #ffffff;
}

.floating-btn.recording:hover {
  background: rgba(255, 120, 117, 0.9);
  border-color: #ff7875;
  transform: scale(1.1);
}

.floating-btn .svg-icon {
  font-size: 20px;
}

/* 鼠标坐标显示 */
.mouse-coordinates {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  min-width: 300px;
}

.coordinates-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.coordinate-line {
  display: flex;
  align-items: center;
}

.coordinate-system {
  font-weight: bold;
  margin-right: 5px;
  min-width: 50px;
}

.coordinate-value {
  font-family: monospace;
}

/* 地图缩放倍数显示 */
.map-zoom-display {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.zoom-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.zoom-label {
  font-weight: bold;
  margin-right: 4px;
}

.zoom-value {
  font-family: monospace;
  font-weight: bold;
}

/* 地图工具栏 */
.map-tools {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.map-tool-btn {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.map-tool-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.map-tool-btn.active-tool {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.map-tool-btn.active-tool:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.map-tool-btn .svg-icon {
  font-size: 18px;
}

/* 视角状态指示器 */
.view-mode-indicator {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 6px 12px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.view-mode-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 距离标签样式 */
.distance-label {
  position: relative;
  pointer-events: none;
}

.distance-text {
  color: #1890ff;
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* 地图工具栏 */
.map-toolbar {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  display: flex;
  gap: 10px;
  background: white;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 右侧航线设置面板 */
.airline-sidebar {
  width: 350px;
  background: #2c2c2c;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  z-index: 1000;
  position: relative;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #404040;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center; /* 让内容居中 */
  min-height: 32px; /* 确保有足够高度容纳按钮 */
}

.header-buttons {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  gap: 8px;
  align-items: center;
}

.save-button {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;

  &:hover {
    background: #40a9ff !important;
    border-color: #40a9ff !important;
  }
}

.close-button {
  background: #8c8c8c !important;
  border-color: #8c8c8c !important;
  color: #ffffff !important;

  &:hover {
    background: #a6a6a6 !important;
    border-color: #a6a6a6 !important;
  }
}

.header-title-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
  /* 确保标题区域不受按钮影响 */
  padding-right: 0;
}

.sidebar-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

.wayline-mode-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  .mode-label {
    color: #999999;
  }

  .mode-value {
    color: #1890ff;
    font-weight: 500;
    background: rgba(24, 144, 255, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    border: 1px solid rgba(24, 144, 255, 0.3);
  }
}

/* 设置区块 */
.setting-section {
  padding: 16px;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

/* 航点列表区块特殊处理 */
.setting-section:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 12px;
  font-weight: 500;
}

.section-title.expandable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

}

.section-title.expandable i {
  transition: transform 0.3s;
}

.section-title.expandable i.collapsed {
  transform: rotate(180deg);
}

.title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-actions .el-button {
  color: #666;
}

.title-actions .el-button:hover {
  color: #1890ff;
}



/* 速度控制 */
.speed-control {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.speed-control.small {
  gap: 8px;
}

.speed-btn {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #404040;
  background: #1a1a1a;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.speed-control.small .speed-btn {
  width: 24px;
  height: 24px;
  font-size: 14px;
}

.speed-btn:hover:not(:disabled) {
  background: #1890ff;
  border-color: #1890ff;
}

.speed-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.speed-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.speed-value {
  font-size: 24px;
  color: #1890ff;
  font-weight: bold;
  line-height: 1;
}

.speed-control.small .speed-value {
  font-size: 16px;
}

.speed-unit {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

/* 全局设置和高级设置 */
.global-settings,
.advanced-settings {
  margin-top: 12px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 12px;
  color: #ccc;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.setting-label i {
  color: #666;
  cursor: help;
}

/* 文件名输入框样式 */
.filename-input {
  width: 100%;
}

.filename-input .el-input__inner {
  background: #666666;
  border-color: #404040;
  color: #ffffff;
}

.filename-input .el-input__inner:focus {
  border-color: #1890ff;
}

.filename-input .el-input__count {
  color: #999;
}

/* 内联文件名输入框样式 */
.filename-input-inline {
  flex: 1;
  margin-left: 8px;
}

.filename-input-inline .el-input__inner {
  background: #666666;
  border-color: #404040;
  color: #ffffff;
}

.filename-input-inline .el-input__inner:focus {
  border-color: #1890ff;
}

.filename-input-inline .el-input__count {
  color: #999;
}

.full-width {
  width: 100%;
}

/* 起飞点设置 */
.takeoff-point-btn-container {
  position: relative;
  margin-bottom: 8px;
}

.takeoff-point-btn {
  width: 100%;
}

.clear-takeoff-btn-float {
  position: absolute;
  top: 4px;
  right: 8px;
  z-index: 10;
  padding: 2px 6px !important;
  font-size: 11px !important;
  color: #ffffff !important;
  background: #1890ff !important;
  border-color: #1890ff !important;
  border-radius: 3px !important;
  min-width: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.clear-takeoff-btn-float:hover {
  color: #ffffff !important;
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

.takeoff-point-info {
  background: #1a1a1a;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
}

.takeoff-point-info .coord-line {
  display: flex;
  margin-bottom: 2px;
}

.takeoff-point-info .coord-line-single {
  display: flex;
  margin-bottom: 2px;
}

.takeoff-point-info .coord-label {
  min-width: 40px;
  color: #666;
}

.takeoff-point-info .coord-value {
  font-family: monospace;
  color: #ccc;
}

.clear-takeoff-btn {
  margin-top: 8px;
  color: #f56c6c;
}

.clear-takeoff-btn:hover {
  color: #ff4d4f;
}

/* 起飞点标记样式 */
.takeoff-point-marker {
  cursor: crosshair;
  transition: all 0.3s;
  pointer-events: none !important; /* 强制让点击穿透 */
}

.takeoff-point-marker.mouse-follow {
  pointer-events: none !important; /* 跟随鼠标的标记完全不响应点击 */
}

.takeoff-point-marker:hover {
  transform: scale(1.1);
}

.takeoff-point-marker.fixed {
  cursor: pointer;
  pointer-events: auto; /* 固定的起飞点可以点击 */
}

.takeoff-point-marker.fixed:hover {
  transform: scale(1.1);
}

/* Element UI 组件样式覆盖 */
.airline-sidebar .el-select {
  width: 100%;
}

.airline-sidebar .el-select .el-input__inner {
  background: #666666;
  border-color: #404040;
  color: #ffffff;
}

.airline-sidebar .el-select .el-input__inner:focus {
  border-color: #1890ff;
}

.airline-sidebar .el-button {
  background: #1a1a1a;
  border-color: #404040;
  color: #ffffff;
}

.airline-sidebar .el-button:hover {
  background: #1890ff;
  border-color: #1890ff;
}



/* 航点列表 */
.waypoints-list {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}

.waypoint-item {
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.waypoint-item:hover {
  border-color: #1890ff;
  background: #262626;
}

.waypoint-item.selected {
  border-color: #1890ff;
  background: #0f1419;
}

.waypoint-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.waypoint-number {
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
}

.waypoint-drag-handle {
  cursor: move;
  color: #666;
  margin-right: 8px;
}

.waypoint-drag-handle:hover {
  color: #1890ff;
}

.waypoint-title {
  flex: 1;
  font-weight: 500;
  color: #ffffff;
  font-size: 12px;
  font-family: monospace;
}

.waypoint-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 航点设置行 */
.waypoint-settings {
  margin-bottom: 8px;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.setting-item-inline {
  display: flex;
  align-items: center;
  gap: 4px;
}

.setting-label {
  color: #ccc;
  font-size: 12px;
  white-space: nowrap;
  min-width: 30px;
}

.setting-input {
  width: 60px;
}

.setting-unit {
  color: #999;
  font-size: 11px;
  margin-left: 2px;
}

.setting-input .el-input__inner {
  background: #1a1a1a;
  border: 1px solid #404040;
  color: #ffffff;
  font-size: 12px;
  height: 28px;
  padding: 0 8px;
}

.setting-input .el-input__inner:focus {
  border-color: #1890ff;
}

.setting-input .el-input-group__append {
  background: #404040;
  border-color: #404040;
  color: #ccc;
  font-size: 10px;
  padding: 0 8px;
}

.coordinate-info {
  font-size: 12px;
  color: #999;
}

.coord-line {
  display: flex;
  margin-bottom: 2px;
}

.coord-label {
  min-width: 40px;
  color: #666;
}

.coord-value {
  font-family: monospace;
  color: #ccc;
}

.waypoint-actions {
  display: flex;
  align-items: center;
}

.action-icons {
  display: flex;
  gap: 6px;
  align-items: center;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.action-icon {
  font-size: 16px;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s;
}

.action-icon:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

.action-text {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  line-height: 1;
}

.action-text:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 动作拖拽样式 */
.actions-draggable {
  display: flex;
  gap: 6px;
  align-items: center;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 2px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.action-item:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: #1890ff;
}

.action-item.sortable-chosen {
  background: rgba(24, 144, 255, 0.2);
  border-color: #1890ff;
  transform: scale(1.05);
}

.action-item.sortable-ghost {
  opacity: 0.5;
  background: rgba(255, 255, 255, 0.1);
}

.action-drag-handle {
  cursor: grab;
  color: #666666;
  font-size: 8px;
  padding: 1px;
  border-radius: 2px;
  transition: all 0.2s ease;
  opacity: 0.6;
}

.action-drag-handle:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  opacity: 1;
}

.action-drag-handle:active {
  cursor: grabbing;
}

.action-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

/* 航点标记样式 */
.waypoint-marker {
  position: relative;
  cursor: pointer;
}

.waypoint-circle {
  width: 28px;
  height: 28px;
  background: #1890ff;
  border: 2px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.4);
  transition: all 0.3s ease;
  position: relative;
}

.waypoint-circle .waypoint-number {
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.waypoint-marker:hover .waypoint-circle {
  transform: scale(1.15);
  background: #40a9ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.6);
}

.waypoint-marker.selected .waypoint-circle {
  background: #52c41a;
  border-color: #ffffff;
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.6);
  transform: scale(1.1);
}

/* 动作配置 */
.waypoint-basic-info {
  margin-bottom: 16px;
}

.actions-config {
  max-height: 300px;
  overflow-y: auto;
}

.add-action {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  align-items: center;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-item {
  border-radius: 4px;
  background: #000000;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.action-params {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-item label {
  min-width: 80px;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .airline-sidebar {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .airline-container {
    flex-direction: column;
  }

  .airline-sidebar {
    width: 100%;
    height: 40vh;
  }

  .map-wrapper {
    height: 60vh;
  }
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item i {
  margin-right: 8px;
  color: #f56c6c;
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

/* 动作图标选中状态 */
.action-item.selected .action-icon-wrapper {
  background: rgba(24, 144, 255, 0.3) !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 动作参数设置面板样式 */
.action-params-panel {
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-top: 8px;
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #404040;
}

.action-waypoint-info {
  font-size: 12px;
  color: #999999;
}

.param-group {
  margin-bottom: 16px;
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.param-label {
  min-width: 80px;
  font-size: 12px;
  color: #cccccc;
  text-align: right;
}

.param-input {
  flex: 1;
  max-width: 120px;
}

.param-input .el-input__inner,
.param-input .el-input-number__input {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
  font-size: 12px !important;
  height: 28px !important;
  line-height: 28px !important;
}

.param-input .el-input__inner:focus,
.param-input .el-input-number__input:focus {
  border-color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.param-input .el-select .el-input__inner {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}

.param-input .el-input-number .el-input-number__decrease,
.param-input .el-input-number .el-input-number__increase {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: #404040 !important;
  color: #cccccc !important;
}

.param-input .el-input-number .el-input-number__decrease:hover,
.param-input .el-input-number .el-input-number__increase:hover {
  background: rgba(24, 144, 255, 0.2) !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.param-unit {
  font-size: 12px;
  color: #999999;
  min-width: 30px;
}

.param-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #404040;
}

.save-btn,
.reset-btn {
  flex: 1;
  height: 32px !important;
  font-size: 12px !important;
}

.save-btn {
  background: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.save-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: #404040 !important;
  color: #cccccc !important;
}

.reset-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: #666666 !important;
}

/* 动作参数对话框样式 */
.action-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.action-waypoint-info {
  font-size: 14px;
  color: #999999;
}

.param-form {
  margin-top: 16px;
}

.param-form .el-form-item {
  margin-bottom: 16px;
}

.param-form .el-form-item__label {
  color: #cccccc !important;
  font-size: 13px !important;
}

.param-form .el-input__inner,
.param-form .el-input-number__input {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}

.param-form .el-input__inner:focus,
.param-form .el-input-number__input:focus {
  border-color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.param-form .el-select .el-input__inner {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid #404040 !important;
  color: #ffffff !important;
}

.param-form .el-input-number .el-input-number__decrease,
.param-form .el-input-number .el-input-number__increase {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: #404040 !important;
  color: #cccccc !important;
}

.param-form .el-input-number .el-input-number__decrease:hover,
.param-form .el-input-number .el-input-number__increase:hover {
  background: rgba(24, 144, 255, 0.2) !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

/* 对话框样式覆盖 */
.el-dialog {
  background: #1a1a1a !important;
  border: 1px solid #404040 !important;
}

.el-dialog__header {
  background: #2a2a2a !important;
  border-bottom: 1px solid #404040 !important;
}

.el-dialog__title {
  color: #ffffff !important;
}

.el-dialog__headerbtn .el-dialog__close {
  color: #cccccc !important;
}

.el-dialog__headerbtn .el-dialog__close:hover {
  color: #ffffff !important;
}

.el-dialog__body {
  background: #1a1a1a !important;
  color: #cccccc !important;
}

.el-dialog__footer {
  background: #2a2a2a !important;
  border-top: 1px solid #404040 !important;
}

/* 批量编辑对话框样式 */
.batch-edit-content {
  padding: 16px 0;
}

.batch-edit-content .el-form-item {
  margin-bottom: 20px;
}

.batch-edit-content .el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.batch-edit-content .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 3D航点标记样式 */
.waypoint-marker-3d {
  transform-style: preserve-3d;
  transition: all 0.3s ease;
}

.waypoint-marker-3d:hover {
  transform: scale(1.1);
}

.waypoint-3d-container {
  transform-style: preserve-3d;
}

/* 高度指示线动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.6);
  }
}

/* 3D圆圈悬停效果 */
.waypoint-circle-3d:hover {
  transform: translateZ(20px) scale(1.05);
  box-shadow:
    0 6px 16px rgba(24, 144, 255, 0.5),
    inset 0 2px 4px rgba(255, 255, 255, 0.4);
}

/* 新的航点样式 - 增强3D效果 */
.waypoint-marker-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.waypoint-3d-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-style: preserve-3d;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.waypoint-circle-main {
  position: relative;
  z-index: 100;
  transform-style: preserve-3d;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.waypoint-circle-inner {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transform-style: preserve-3d;
  /* 增强3D立体感 */
  box-shadow:
    0 4px 12px rgba(56, 158, 13, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.waypoint-circle-inner:hover {
  transform: scale(1.1) rotateX(5deg);
  box-shadow:
    0 8px 20px rgba(56, 158, 13, 0.5),
    inset 0 3px 6px rgba(255, 255, 255, 0.4),
    0 0 0 2px rgba(255, 255, 255, 0.2);
}

.height-line {
  position: relative;
  z-index: 50;
  transition: height 0.2s ease-out, transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.height-line-inner {
  position: relative;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  /* 增强3D立体感 */
  box-shadow:
    2px 0 4px rgba(82, 196, 26, 0.3),
    -2px 0 4px rgba(82, 196, 26, 0.2);
}

.height-line-inner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 1px;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  transform: translateX(-50%);
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.6);
}

.height-line-inner::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 1px;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  transform: translateZ(-1px);
}

.ground-point {
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  /* 地面投影效果 */
  box-shadow:
    0 0 4px rgba(82, 196, 26, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  /* 确保始终在地面层 */
  transform: translateZ(0px) !important;
}

.ground-point:hover {
  transform: translateZ(0px) scale(1.2) !important;
  background: rgba(82, 196, 26, 0.8);
  box-shadow:
    0 0 8px rgba(82, 196, 26, 0.5),
    inset 0 1px 2px rgba(255, 255, 255, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.15);
}

/* 高度拖拽时的视觉反馈 */
.height-dragging {
  cursor: ns-resize !important;
}

.height-dragging .waypoint-circle-inner {
  animation: heightPulse 0.8s infinite alternate;
  cursor: ns-resize !important;
}

.height-dragging .height-line-inner {
  animation: linePulse 1s infinite alternate;
}

@keyframes heightPulse {
  0% {
    background: radial-gradient(circle at 30% 30%, #52c41a, #389e0d);
    box-shadow: 0 4px 12px rgba(56, 158, 13, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }
  100% {
    background: radial-gradient(circle at 30% 30%, #ff7875, #f5222d);
    box-shadow: 0 6px 16px rgba(245, 34, 45, 0.6), inset 0 2px 4px rgba(255, 255, 255, 0.4);
  }
}

@keyframes linePulse {
  0% {
    box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);
  }
  100% {
    box-shadow: 0 0 12px rgba(245, 34, 45, 0.8);
  }
}

/* 3D球体浮动动画 */
@keyframes sphereFloat {
  0%, 100% {
    transform: translateY(0px) rotateY(0deg);
  }
  25% {
    transform: translateY(-2px) rotateY(90deg);
  }
  50% {
    transform: translateY(-4px) rotateY(180deg);
  }
  75% {
    transform: translateY(-2px) rotateY(270deg);
  }
}

/* 3D航点标记样式 */
.waypoint-marker-3d {
  transform-style: preserve-3d;
}

.waypoint-3d-scene {
  transform-style: preserve-3d;
}

.waypoint-3d-container {
  transform-style: preserve-3d;
}

.waypoint-sphere-container {
  transform-style: preserve-3d;
}

.waypoint-sphere {
  transform-style: preserve-3d;
}

.height-pillar {
  transform-style: preserve-3d;
}

/* 选中状态的3D效果 */
.waypoint-marker-3d.selected .waypoint-3d-container {
  animation: selectedPulse 2s ease-in-out infinite;
}

@keyframes selectedPulse {
  0%, 100% {
    transform: rotateX(15deg) rotateY(5deg) scale(1);
  }
  50% {
    transform: rotateX(20deg) rotateY(10deg) scale(1.05);
  }
}
</style>