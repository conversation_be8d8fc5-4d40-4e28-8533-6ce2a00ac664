<template>
  <div class="three-flight-path-test">
    <div class="test-header">
      <h2>3D航线路径测试页面</h2>
      <div class="test-controls">
        <el-button @click="enable3DFlightPath" type="primary">启用3D航线</el-button>
        <el-button @click="disable3DFlightPath">禁用3D航线</el-button>
        <el-button @click="createTestFlightPath" type="success">创建测试航线</el-button>
        <el-button @click="addAircraft" type="warning">添加飞行器</el-button>
        <el-button @click="clearAll" type="danger">清空所有</el-button>
      </div>
      <div class="test-info">
        <p>3D航线状态: <span :class="use3DFlightPath ? 'enabled' : 'disabled'">{{ use3DFlightPath ? '已启用' : '已禁用' }}</span></p>
        <p>航线数量: {{ flightPaths.length }}</p>
        <p>飞行器数量: {{ aircrafts.length }}</p>
        <p>点击地图创建航点，多个航点自动形成3D航线</p>
      </div>
    </div>
    
    <div class="map-container">
      <Amap ref="amap" :coordinates="[]" :zoom="13" @map-click="onMapClick" />
    </div>
    
    <div class="control-panel">
      <div class="waypoint-section">
        <h3>航点列表</h3>
        <div v-if="waypoints.length === 0" class="empty-list">
          暂无航点，点击地图创建航点
        </div>
        <div v-else class="waypoint-items">
          <div v-for="(waypoint, index) in waypoints" :key="waypoint.id" class="waypoint-item">
            <div class="waypoint-info">
              <span class="waypoint-index">P{{ index + 1 }}</span>
              <span class="waypoint-coords">
                {{ waypoint.longitude.toFixed(6) }}, {{ waypoint.latitude.toFixed(6) }}
              </span>
              <span class="waypoint-height">{{ waypoint.height }}m</span>
            </div>
            <div class="waypoint-actions">
              <el-input-number 
                v-model="waypoint.height" 
                :min="10" 
                :max="500" 
                size="mini"
                @change="updateFlightPath"
                style="width: 80px;">
              </el-input-number>
              <el-button @click="removeWaypoint(index)" type="danger" size="mini">删除</el-button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flight-path-section">
        <h3>航线配置</h3>
        <div class="config-item">
          <label>路径颜色:</label>
          <el-color-picker v-model="pathColor" @change="updateFlightPath"></el-color-picker>
        </div>
        <div class="config-item">
          <label>路径宽度:</label>
          <el-slider v-model="pathWidth" :min="2" :max="20" @change="updateFlightPath"></el-slider>
        </div>
        <div class="config-item">
          <label>飞行速度:</label>
          <el-slider v-model="aircraftSpeed" :min="10" :max="200" @change="updateAircraftSpeed"></el-slider>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Amap from '@/components/Amap/index.vue'
import { ThreeFlightPathManager } from '@/utils/ThreeFlightPathManager.js'
import mapMixin from '@/views/flightmaster/mixins/mapMixin.js'

export default {
  name: 'ThreeFlightPathTest',
  components: {
    Amap
  },
  mixins: [mapMixin],
  data() {
    return {
      use3DFlightPath: false,
      threeFlightPathManager: null,
      waypoints: [],
      flightPaths: [],
      aircrafts: [],
      waypointCounter: 0,
      aircraftCounter: 0,
      
      // 配置参数
      pathColor: '#00ff00',
      pathWidth: 8,
      aircraftSpeed: 50
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    if (this.threeFlightPathManager) {
      this.threeFlightPathManager.dispose()
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      this.$nextTick(() => {
        if (this.$refs.amap && this.$refs.amap.map) {
          console.log('3D航线测试页面地图初始化完成')
          // 切换到3D视角
          setTimeout(() => {
            this.switch3DView()
          }, 1000)
        } else {
          setTimeout(() => {
            this.initMap()
          }, 500)
        }
      })
    },
    
    // 切换到3D视角
    switch3DView() {
      try {
        const map = this.$refs.amap.map
        if (map && map.setViewMode) {
          map.setViewMode('3D')
          map.setPitch(45)
          console.log('已切换到3D视角')
        }
      } catch (error) {
        console.error('切换3D视角失败:', error)
      }
    },
    
    // 启用3D航线
    enable3DFlightPath() {
      if (!this.$refs.amap || !this.$refs.amap.map) {
        this.$message.error('地图未初始化')
        return
      }
      
      this.use3DFlightPath = true
      
      setTimeout(() => {
        this.initThreeFlightPathManager()
      }, 1000)
    },
    
    // 禁用3D航线
    disable3DFlightPath() {
      this.use3DFlightPath = false
      if (this.threeFlightPathManager) {
        this.threeFlightPathManager.dispose()
        this.threeFlightPathManager = null
      }
      this.flightPaths = []
      this.aircrafts = []
      this.$message.success('3D航线已禁用')
    },
    
    // 初始化3D航线管理器
    initThreeFlightPathManager() {
      try {
        if (this.$refs.amap && this.$refs.amap.map) {
          const map = this.$refs.amap.map
          
          if (!map.customCoords) {
            console.warn('customCoords未准备好，延迟初始化')
            setTimeout(() => {
              this.initThreeFlightPathManager()
            }, 1000)
            return
          }
          
          this.threeFlightPathManager = new ThreeFlightPathManager(map)
          this.$message.success('3D航线管理器初始化成功')
          console.log('3D航线管理器初始化成功')
          
          // 如果已有航点，创建航线
          if (this.waypoints.length >= 2) {
            this.createFlightPath()
          }
        }
      } catch (error) {
        console.error('3D航线管理器初始化失败:', error)
        this.$message.error('3D航线初始化失败: ' + error.message)
        this.use3DFlightPath = false
      }
    },
    
    // 地图点击事件
    onMapClick(lnglat) {
      console.log('地图点击:', lnglat)
      this.addWaypoint(lnglat.lng, lnglat.lat)
    },
    
    // 添加航点
    addWaypoint(lng, lat, height = 100) {
      const wgs84 = this.gcj02ToWgs84(lng, lat)
      
      const waypoint = {
        id: `waypoint_${++this.waypointCounter}`,
        longitude: wgs84.lng,
        latitude: wgs84.lat,
        height: height
      }
      
      this.waypoints.push(waypoint)
      
      // 如果有2个或以上航点，创建/更新航线
      if (this.waypoints.length >= 2 && this.use3DFlightPath && this.threeFlightPathManager) {
        this.createFlightPath()
      }
      
      this.$message.success(`航点 P${this.waypoints.length} 创建成功`)
      console.log('添加航点:', waypoint)
    },
    
    // 创建航线
    createFlightPath() {
      if (!this.use3DFlightPath || !this.threeFlightPathManager || this.waypoints.length < 2) {
        return
      }
      
      try {
        // 删除现有航线
        this.threeFlightPathManager.removeFlightPath('test_path')
        
        // 创建新航线
        const result = this.threeFlightPathManager.createFlightPath(
          'test_path',
          this.waypoints,
          {
            name: '测试航线',
            color: parseInt(this.pathColor.replace('#', '0x'))
          }
        )
        
        if (result) {
          this.flightPaths = ['test_path']
          console.log('3D航线创建成功:', result)
        }
      } catch (error) {
        console.error('3D航线创建失败:', error)
        this.$message.error('3D航线创建失败: ' + error.message)
      }
    },
    
    // 创建测试航线
    createTestFlightPath() {
      // 清空现有航点
      this.waypoints = []
      this.waypointCounter = 0
      
      // 创建测试航点
      const center = this.$refs.amap.map.getCenter()
      const testWaypoints = [
        { lng: center.lng - 0.01, lat: center.lat - 0.005, height: 100 },
        { lng: center.lng - 0.005, lat: center.lat, height: 150 },
        { lng: center.lng, lat: center.lat + 0.005, height: 200 },
        { lng: center.lng + 0.005, lat: center.lat, height: 150 },
        { lng: center.lng + 0.01, lat: center.lat - 0.005, height: 100 }
      ]
      
      testWaypoints.forEach(wp => {
        this.addWaypoint(wp.lng, wp.lat, wp.height)
      })
      
      this.$message.success('测试航线已创建')
    },
    
    // 添加飞行器
    addAircraft() {
      if (!this.use3DFlightPath || !this.threeFlightPathManager || this.flightPaths.length === 0) {
        this.$message.warning('请先创建3D航线')
        return
      }
      
      try {
        const aircraftId = `aircraft_${++this.aircraftCounter}`
        const result = this.threeFlightPathManager.createAircraft(
          aircraftId,
          'test_path',
          {
            speed: this.aircraftSpeed
          }
        )
        
        if (result) {
          this.aircrafts.push(aircraftId)
          this.$message.success(`飞行器 ${aircraftId} 已添加`)
        }
      } catch (error) {
        console.error('添加飞行器失败:', error)
        this.$message.error('添加飞行器失败: ' + error.message)
      }
    },
    
    // 删除航点
    removeWaypoint(index) {
      this.waypoints.splice(index, 1)
      
      if (this.waypoints.length >= 2) {
        this.createFlightPath()
      } else {
        // 航点不足，删除航线
        if (this.threeFlightPathManager) {
          this.threeFlightPathManager.removeFlightPath('test_path')
          this.flightPaths = []
          this.aircrafts = []
        }
      }
      
      this.$message.success('航点已删除')
    },
    
    // 更新航线
    updateFlightPath() {
      if (this.waypoints.length >= 2) {
        this.createFlightPath()
      }
    },
    
    // 更新飞行器速度
    updateAircraftSpeed() {
      if (this.threeFlightPathManager && this.aircrafts.length > 0) {
        this.aircrafts.forEach(aircraftId => {
          const aircraft = this.threeFlightPathManager.aircrafts.get(aircraftId)
          if (aircraft) {
            aircraft.userData.speed = this.aircraftSpeed
          }
        })
      }
    },
    
    // 清空所有
    clearAll() {
      this.waypoints = []
      this.flightPaths = []
      this.aircrafts = []
      this.waypointCounter = 0
      this.aircraftCounter = 0
      
      if (this.threeFlightPathManager) {
        this.threeFlightPathManager.removeFlightPath('test_path')
      }
      
      this.$message.success('已清空所有内容')
    }
  }
}
</script>

<style scoped>
.three-flight-path-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-header {
  padding: 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.test-header h2 {
  margin: 0 0 15px 0;
  color: #333;
}

.test-controls {
  margin-bottom: 15px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.test-info p {
  margin: 5px 0;
  font-size: 14px;
}

.enabled {
  color: #67c23a;
  font-weight: bold;
}

.disabled {
  color: #f56c6c;
  font-weight: bold;
}

.map-container {
  flex: 1;
  position: relative;
}

.control-panel {
  height: 300px;
  display: flex;
  background: #fafafa;
  border-top: 1px solid #ddd;
}

.waypoint-section, .flight-path-section {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.waypoint-section {
  border-right: 1px solid #ddd;
}

.waypoint-section h3, .flight-path-section h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.empty-list {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.waypoint-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.waypoint-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.waypoint-index {
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
}

.waypoint-coords {
  font-size: 12px;
  color: #666;
  margin: 2px 0;
}

.waypoint-height {
  font-size: 12px;
  color: #67c23a;
  font-weight: bold;
}

.waypoint-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.config-item label {
  width: 80px;
  font-size: 14px;
  color: #333;
}

.config-item .el-slider {
  flex: 1;
  margin-left: 10px;
}
</style>
